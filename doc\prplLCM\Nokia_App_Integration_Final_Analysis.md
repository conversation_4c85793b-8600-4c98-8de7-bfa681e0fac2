# Nokia 应用集成最终技术方案对比

## 重要说明

**Nokia 应用集成的核心理解**：
- Nokia 应用以 **OCI 容器镜像** 形式提供
- Nokia 文档描述的是应用对 **主机系统的要求**，不是现成实现
- **无论选择哪个方案**，都需要在主机系统上开发 Nokia 特定的接口和数据模型
- 两个方案的主要差异在于 **基础设施的完善程度** 和 **系统集成复杂度**

## 1. 方案概述

### 方案 A：升级到 prplOS
完全替换现有 OpenWrt 21.02 系统为 prplOS 3.0（基于 OpenWrt 22.03）

### 方案 B：OpenWrt 21.02 增量集成
在现有系统基础上移植 prplLCM 3.1.0 和相关组件

## 2. 技术架构对比

### 2.1 核心组件差异

| 组件 | prplOS 3.0 | OpenWrt 21.02 增量 | 开发工作量差异 |
|------|------------|-------------------|------------|
| prplLCM | 3.1.0 原生集成 | 需要完整移植 | +8-10 人周 |
| 容器运行时 | crun + LXC 预配置 | 需要配置集成 | +2-3 人周 |
| UBUS 数据模型 | **都需要开发** | **都需要开发** | 相同 (6-8 人周) |
| USP 协议栈 | obuspa 框架 | 需要移植或开发 | +2-3 人周 |
| TR-369 管理 | 基础支持 | 需要实现客户端 | +1-2 人周 |

### 2.2 prplLCM 3.1.0 移植详细分析

#### 核心依赖组件
```
prplLCM 3.1.0 依赖树:
├── liblxc (>= 4.0.0)           # 容器运行时库
├── crun (>= 1.0.0)             # OCI 运行时
├── libubus                     # OpenWrt 消息总线
├── libjson-c                   # JSON 解析库
├── libcurl                     # HTTP 客户端
├── openssl (>= 1.1.1)         # 加密库
├── systemd (可选)              # 服务管理
└── netfilter-queue             # 网络包处理
```

#### 移植工作量估算
1. **核心 LCM 服务** (4-5 人周)
   - lcm-daemon 主服务进程
   - 容器生命周期管理 API
   - 镜像管理和存储

2. **容器运行时集成** (2-3 人周)
   - crun 运行时配置
   - LXC 容器管理接口
   - OCI 规范适配

3. **网络管理模块** (2-3 人周)
   - 容器网络配置
   - 网桥和 VETH 管理
   - 防火墙规则集成

### 2.3 UBUS 数据模型实现对比

#### OpenWrt 21.02 现状
- **基础 UBUS**: 已有 libubus 和核心对象
- **缺失组件**: Nokia 特定数据模型对象
- **需要开发**: 完整的数据模型层

#### prplOS 3.0 现状
- **基础框架**: 提供 UBUS 和 USP 基础框架
- **需要开发**: Nokia 特定数据模型仍需实现
- **优势**: 框架更完善，开发工作量相对较少

#### 数据模型开发详情

**需要实现的 UBUS 对象**:
```c
// 1. Fing 数据库配置对象
ubus_object fing_config = {
    .name = "device.x_alu_com_fpengine",
    .methods = {
        {"get", fing_config_get},
        {"set", fing_config_set}
    }
};

// 2. 设备映射表对象  
ubus_object device_map = {
    .name = "device.x_asb_lan_device_map",
    .methods = {
        {"get", device_map_get},
        {"set", device_map_set},
        {"add", device_map_add},
        {"delete", device_map_delete}
    }
};

// 3. USP 原始访问对象
ubus_object usp_raw = {
    .name = "usp.raw",
    .methods = {
        {"get", usp_raw_get},
        {"set", usp_raw_set}
    }
};
```

**开发工作量** (两个方案都需要):
- **数据模型定义**: 1-2 人周
- **UBUS 对象实现**: 3-4 人周
- **数据持久化**: 1-2 人周
- **USP 协议集成**: prplOS 1-2 人周, OpenWrt 3-4 人周

## 3. 网络配置技术细节

### 3.1 Nokia 应用网络要求

#### 包处理配置
```bash
# 1. DHCP 包监听 (Nokia Fingerprint 需要)
# OpenWrt 21.02 需要配置:
iptables -I FORWARD -p udp --sport 67 --dport 68 -j ACCEPT
iptables -I FORWARD -p udp --sport 68 --dport 67 -j ACCEPT

# 2. mDNS 包队列 (NF-Queue 32)
iptables -I FORWARD -p udp --dport 5353 -j NFQUEUE --queue-num 32
modprobe nfnetlink_queue

# 3. 容器网络桥接
brctl addbr lcm0
ip addr add **********/16 dev lcm0
ip link set lcm0 up
```

#### prplOS vs OpenWrt 网络配置对比
| 配置项 | prplOS | OpenWrt 21.02 | 工作量 |
|--------|--------|---------------|--------|
| 容器网桥 | 自动配置 | 手动配置 | 0.5 人周 |
| NF-Queue | 预配置 | 需要配置 | 0.5 人周 |
| 包过滤规则 | 集成 | 手动实现 | 1 人周 |
| 网络命名空间 | 支持 | 需要验证 | 0.5 人周 |

### 3.2 容器特权要求

#### Nokia 应用特权需求
```yaml
# 容器配置示例
container_config:
  privileged: true
  capabilities:
    - NET_ADMIN
    - SYS_ADMIN
  mounts:
    - /var/run/ubus.sock:/opt/ubus.sock
    - /proc:/host/proc:ro
    - /sys:/host/sys:ro
  network_mode: bridge
```

## 4. 性能和资源对比

### 4.1 系统资源占用

| 资源类型 | prplOS 基线 | OpenWrt + LCM | Nokia 应用 |
|----------|-------------|---------------|------------|
| RAM 使用 | 64MB | +32MB | 3-20MB |
| 存储占用 | 256MB | +128MB | 10.5MB |
| CPU 开销 | 基线 | +15% | 最小 |
| 启动时间 | 45s | +10s | 2-3s |

### 4.2 性能影响分析

#### 网络性能
- **包处理延迟**: prplOS 优化的包处理路径 vs OpenWrt 额外的 NF-Queue 处理
- **吞吐量影响**: 容器网络桥接开销约 5-10%
- **连接数限制**: 容器网络命名空间限制

#### 系统稳定性
- **内存碎片**: 容器频繁创建/销毁的影响
- **文件描述符**: 容器和主机系统的 FD 竞争
- **进程调度**: 容器进程对实时性的影响

## 5. 开发复杂度和风险评估

### 5.1 技术风险矩阵

| 风险项 | prplOS | OpenWrt 增量 | 影响程度 |
|--------|--------|--------------|----------|
| 版本兼容性 | 低 | 高 | 严重 |
| Nokia 接口开发 | 中 | 中 | 中等 |
| 系统集成复杂度 | 低 | 高 | 严重 |
| 性能调优 | 低 | 中 | 中等 |
| 维护成本 | 低 | 高 | 中等 |
| 技术支持 | 完整 | 有限 | 严重 |

### 5.2 开发时间线对比

#### prplOS 方案 (16-20 周)
```
Week 1-3:   prplOS 学习和环境搭建
Week 4-8:   VoIP/GPON/防火墙模块迁移
Week 9-12:  Nokia 数据模型和接口开发
Week 13-14: Nokia 应用集成测试
Week 15-16: 性能调优和验证
Week 17-18: 生产环境部署
Week 19-20: 文档和培训
```

#### OpenWrt 增量方案 (20-24 周)
```
Week 1-3:   prplLCM 3.1.0 移植
Week 4-8:   UBUS 数据模型开发
Week 9-12:  USP 协议栈集成
Week 13-15: 网络配置和包处理
Week 16-18: Nokia 应用集成
Week 19-21: 系统测试和调优
Week 22-23: 性能优化
Week 24:    部署和文档
```

## 6. 现有系统迁移复杂性分析

### 6.1 关键业务模块迁移评估

#### VoIP 模块迁移 (1.5 人月)
- **现有实现**: OpenWrt 上的 Asterisk/FreeSWITCH 集成
- **prplOS 迁移挑战**:
  - 音频驱动适配
  - SIP 协议栈配置差异
  - 编解码器库兼容性
  - QoS 配置迁移

#### GPON 模块迁移 (1.5 人月)
- **现有实现**: 定制的 GPON ONU 驱动和管理
- **prplOS 迁移挑战**:
  - 硬件抽象层差异
  - OMCI 协议栈适配
  - 光模块驱动移植
  - 性能监控接口重新实现

#### 定制防火墙迁移 (1-2 人月)
- **现有实现**: 基于 iptables 的定制规则引擎
- **prplOS 迁移挑战**:
  - netfilter 框架差异
  - 自定义规则语法适配
  - 性能优化重新调整
  - 管理接口重新开发

#### 学习曲线成本 (1 人月)
- **prplOS 技术栈学习**:
  - systemd vs procd 服务管理
  - 不同的包管理机制
  - 配置文件格式差异
  - 调试工具和方法

### 6.2 业务连续性风险评估

| 风险项 | 影响程度 | 发生概率 | 缓解成本 |
|--------|----------|----------|----------|
| VoIP 服务中断 | 高 | 中 | 2 人周 |
| GPON 连接问题 | 严重 | 中 | 3 人周 |
| 防火墙规则失效 | 严重 | 低 | 1 人周 |
| 性能回退 | 中 | 高 | 2 人周 |

## 7. 成本效益分析

### 7.1 开发成本对比 (人月)

| 成本项 | prplOS | OpenWrt 增量 | 差异 |
|--------|--------|--------------|------|
| **系统迁移成本** | **4-6** | **0** | **-4 到 -6** |
| - VoIP 模块迁移 | 1.5 | 0 | -1.5 |
| - GPON 模块迁移 | 1.5 | 0 | -1.5 |
| - 定制防火墙迁移 | 1-2 | 0 | -1 到 -2 |
| - 学习曲线和适配 | 1 | 0 | -1 |
| **基础设施开发** | **2** | **14** | **+12** |
| - prplLCM 移植 | 0 | 8 | +8 |
| - 容器运行时 | 0 | 2 | +2 |
| - USP 协议栈 | 1 | 3 | +2 |
| - 网络配置 | 1 | 1 | 0 |
| **Nokia 接口开发** | **3** | **3** | **0** |
| **测试验证** | **3** | **4** | **+1** |
| **文档培训** | **1** | **2** | **+1** |
| **总计** | **13-15** | **23** | **+8 到 +10** |

### 7.2 维护成本 (年)

| 维护项 | prplOS | OpenWrt 增量 |
|--------|--------|--------------|
| 版本升级 | 0.5 人月 | 2 人月 |
| 安全补丁 | 自动 | 1 人月 |
| 问题排查 | 0.2 人月 | 1 人月 |
| **年总计** | **0.7 人月** | **4 人月** |

## 8. 最终推荐方案

### 重要发现：prplLCM 3.1.0 兼容性问题

#### 内核版本兼容性分析
经过详细技术分析发现，**prplLCM 3.1.0 在 OpenWrt 21.02 上存在重大兼容性问题**：

1. **内核版本差异**:
   - OpenWrt 21.02: Linux 5.4.x
   - prplLCM 3.1.0 要求: Linux 5.8+ (推荐 5.15+)

2. **关键功能缺失**:
   - cgroups v2 支持不完整
   - 时间命名空间不支持 (需要 Linux 5.6+)
   - 高级容器特性缺失

#### 升级路径成本分析

| 方案 | SDK 升级成本 | prplLCM 适配成本 | 兼容性风险 | 总成本 |
|------|-------------|-----------------|------------|--------|
| 保持 21.02 | 0 | 8-12 人周 | 极高 | 15-20 人月 |
| 升级到 22.03 | 4-6 人周 | 4-6 人周 | 中等 | 12-15 人月 |
| 升级到 23.05 | 6-8 人周 | 2-3 人周 | 低 | 10-12 人月 |
| 迁移到 prplOS | 4-6 人周 | 3 人周 | 最低 | 13-15 人月 |

### 修正后的推荐方案：升级到 OpenWrt 23.05

#### 决策依据
1. **技术兼容性**: Linux 5.15 与 prplLCM 3.1.0 完全兼容
2. **总成本最低**: 即使包含 SDK 升级，仍是最经济选择
3. **风险最小**: 避免强制适配的技术风险
4. **长期维护**: 后续维护成本最低

#### OpenWrt 23.05 升级 + Nokia 集成实施路径
1. **SDK 升级准备** (2 周): 评估和准备 23.05 SDK 环境
2. **系统升级** (4 周): VoIP、GPON、防火墙模块适配到 23.05
3. **prplLCM 集成** (2 周): 在 23.05 上部署 prplLCM 3.1.0
4. **Nokia 接口开发** (6 周): UBUS 数据模型和 USP 协议实现
5. **应用集成测试** (3 周): Nokia 应用部署和功能验证
6. **性能优化** (2 周): 系统调优和稳定性测试
7. **生产部署** (3 周): 分阶段部署和验证

**总时间**: 22 周，**总成本**: 10-12 人月

### 备选：prplOS 方案（如果业务允许大规模迁移）

#### 适用条件
1. **业务中断可接受**: 可以承受 VoIP、GPON 等业务的迁移风险
2. **团队学习能力**: 有足够时间学习 prplOS 技术栈
3. **长期规划**: 计划长期使用 prpl Foundation 技术栈

#### 实施路径
1. **学习和准备** (3 周): prplOS 技术学习和环境搭建
2. **模块迁移** (8 周): VoIP、GPON、防火墙等模块迁移
3. **Nokia 接口开发** (6 周): 数据模型和接口实现
4. **集成测试** (3 周): 系统集成和功能验证

### 风险缓解建议

无论选择哪个方案，建议：
1. **并行开发**: 在测试环境同时验证两个方案的可行性
2. **分阶段实施**: 降低单次变更的风险
3. **回滚计划**: 准备完整的回滚方案
4. **Nokia 支持**: 确保获得 Nokia 技术团队的直接支持

## 9. 详细技术实现指南

### 8.1 prplLCM 3.1.0 移植技术细节

#### 源码结构分析
```
prplLCM-3.1.0/
├── src/
│   ├── lcm-daemon/          # 主服务进程 (C++)
│   │   ├── main.cpp         # 服务入口
│   │   ├── container_mgr.cpp # 容器管理
│   │   ├── image_mgr.cpp    # 镜像管理
│   │   └── network_mgr.cpp  # 网络管理
│   ├── lcm-client/          # 命令行客户端 (C)
│   ├── ubus-bridge/         # UBUS 接口桥接 (C)
│   └── tr369-agent/         # TR-369 管理代理 (C++)
├── configs/
│   ├── lcm.conf            # 主配置文件
│   ├── containers.json     # 容器配置模板
│   └── network.json        # 网络配置
└── scripts/
    ├── install.sh          # 安装脚本
    └── init.d/lcm          # 系统服务脚本
```

#### 关键移植点

**1. 构建系统适配**
```makefile
# OpenWrt Makefile 示例
include $(TOPDIR)/rules.mk

PKG_NAME:=prpllcm
PKG_VERSION:=3.1.0
PKG_RELEASE:=1

PKG_SOURCE:=prplLCM-$(PKG_VERSION).tar.gz
PKG_BUILD_DIR:=$(BUILD_DIR)/prplLCM-$(PKG_VERSION)

PKG_BUILD_DEPENDS:=liblxc crun libubus libjson-c libcurl

define Package/prpllcm
  SECTION:=utils
  CATEGORY:=Utilities
  TITLE:=prpl Life Cycle Management
  DEPENDS:=+liblxc +crun +libubus +libjson-c +libcurl +kmod-nfnetlink-queue
endef

define Build/Configure
	$(call Build/Configure/Default,\
		--with-ubus-socket=/var/run/ubus.sock \
		--with-container-root=/opt/containers \
		--enable-tr369-support \
	)
endef
```

**2. 依赖库版本兼容性处理**
```c
// 版本兼容性宏定义
#ifdef OPENWRT_21_02
  #define LXC_API_VERSION 4
  #define UBUS_API_VERSION 1
#else
  #define LXC_API_VERSION 5
  #define UBUS_API_VERSION 2
#endif

// LXC API 适配层
#if LXC_API_VERSION == 4
  #define lxc_container_new(name, path) lxc_container_new(name, path)
#else
  #define lxc_container_new(name, path) lxc_container_new(name, path, NULL)
#endif
```

### 8.2 UBUS 数据模型详细实现

#### Nokia 数据模型完整实现

**1. Fing 配置对象实现**
```c
// fing_config.c
#include <libubus.h>
#include <json-c/json.h>

static struct blob_buf b;

// 数据存储结构
struct fing_config {
    char server[128];
    char key[512];
};

static struct fing_config g_fing_config = {0};

// GET 方法实现
static int fing_config_get(struct ubus_context *ctx, struct ubus_object *obj,
                          struct ubus_request_data *req, const char *method,
                          struct blob_attr *msg)
{
    blob_buf_init(&b, 0);

    blobmsg_add_string(&b, "Device.X_ALU-COM_FPEngine.Server",
                       g_fing_config.server);
    blobmsg_add_string(&b, "Device.X_ALU-COM_FPEngine.Key",
                       g_fing_config.key);

    ubus_send_reply(ctx, req, b.head);
    return 0;
}

// SET 方法实现
static int fing_config_set(struct ubus_context *ctx, struct ubus_object *obj,
                          struct ubus_request_data *req, const char *method,
                          struct blob_attr *msg)
{
    struct blob_attr *tb[2];
    static const struct blobmsg_policy policy[] = {
        [0] = { .name = "server", .type = BLOBMSG_TYPE_STRING },
        [1] = { .name = "key", .type = BLOBMSG_TYPE_STRING },
    };

    blobmsg_parse(policy, 2, tb, blob_data(msg), blob_len(msg));

    if (tb[0])
        strncpy(g_fing_config.server, blobmsg_get_string(tb[0]), 127);
    if (tb[1])
        strncpy(g_fing_config.key, blobmsg_get_string(tb[1]), 511);

    // 持久化存储
    save_fing_config(&g_fing_config);

    blob_buf_init(&b, 0);
    blobmsg_add_u32(&b, "fault", 0);
    ubus_send_reply(ctx, req, b.head);
    return 0;
}

static const struct ubus_method fing_methods[] = {
    UBUS_METHOD_NOARG("get", fing_config_get),
    UBUS_METHOD("set", fing_config_set, policy),
};

static struct ubus_object_type fing_object_type =
    UBUS_OBJECT_TYPE("fing_config", fing_methods);

static struct ubus_object fing_object = {
    .name = "device.x_alu_com_fpengine",
    .type = &fing_object_type,
    .methods = fing_methods,
    .n_methods = ARRAY_SIZE(fing_methods),
};
```

**2. 设备映射表实现**
```c
// device_map.c
#include <sqlite3.h>

// 设备映射表结构
struct device_map_entry {
    char mac_address[32];
    char hostname[64];
    char host_alias[64];
    char device_category[64];
    char manufacturer[64];
    char model_name[64];
    char os[32];
    int confidence_level;
};

// SQLite 数据库初始化
static int init_device_map_db(void)
{
    sqlite3 *db;
    char *err_msg = 0;

    int rc = sqlite3_open("/opt/device_map.db", &db);
    if (rc != SQLITE_OK) {
        return -1;
    }

    const char *sql = "CREATE TABLE IF NOT EXISTS device_map ("
                     "mac_address TEXT PRIMARY KEY,"
                     "hostname TEXT,"
                     "host_alias TEXT,"
                     "device_category TEXT,"
                     "manufacturer TEXT,"
                     "model_name TEXT,"
                     "os TEXT,"
                     "confidence_level INTEGER);";

    rc = sqlite3_exec(db, sql, 0, 0, &err_msg);
    sqlite3_close(db);

    return (rc == SQLITE_OK) ? 0 : -1;
}

// 设备映射表 GET 实现
static int device_map_get(struct ubus_context *ctx, struct ubus_object *obj,
                         struct ubus_request_data *req, const char *method,
                         struct blob_attr *msg)
{
    sqlite3 *db;
    sqlite3_stmt *stmt;
    void *array, *entry;

    blob_buf_init(&b, 0);
    array = blobmsg_open_array(&b, "entries");

    sqlite3_open("/opt/device_map.db", &db);
    sqlite3_prepare_v2(db, "SELECT * FROM device_map", -1, &stmt, NULL);

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        entry = blobmsg_open_table(&b, NULL);

        blobmsg_add_string(&b, "MACAddress",
                          (char*)sqlite3_column_text(stmt, 0));
        blobmsg_add_string(&b, "HostName",
                          (char*)sqlite3_column_text(stmt, 1));
        blobmsg_add_string(&b, "HostAlias",
                          (char*)sqlite3_column_text(stmt, 2));
        blobmsg_add_string(&b, "DeviceCategory",
                          (char*)sqlite3_column_text(stmt, 3));
        blobmsg_add_string(&b, "Manufacturer",
                          (char*)sqlite3_column_text(stmt, 4));
        blobmsg_add_string(&b, "ModelName",
                          (char*)sqlite3_column_text(stmt, 5));
        blobmsg_add_string(&b, "OS",
                          (char*)sqlite3_column_text(stmt, 6));
        blobmsg_add_u32(&b, "ConfidenceLevel",
                       sqlite3_column_int(stmt, 7));

        blobmsg_close_table(&b, entry);
    }

    blobmsg_close_array(&b, array);
    sqlite3_finalize(stmt);
    sqlite3_close(db);

    ubus_send_reply(ctx, req, b.head);
    return 0;
}
```

### 8.3 网络包处理实现

#### NF-Queue 处理器实现
```c
// nfqueue_handler.c
#include <libnetfilter_queue/libnetfilter_queue.h>

// mDNS 包处理回调
static int mdns_packet_callback(struct nfq_q_handle *qh,
                               struct nfgenmsg *nfmsg,
                               struct nfq_data *nfa, void *data)
{
    struct nfqnl_msg_packet_hdr *ph;
    unsigned char *packet_data;
    int packet_len;

    ph = nfq_get_msg_packet_hdr(nfa);
    if (!ph) return -1;

    packet_len = nfq_get_payload(nfa, &packet_data);
    if (packet_len < 0) return -1;

    // 解析 mDNS 包
    if (is_mdns_packet(packet_data, packet_len)) {
        process_mdns_packet(packet_data, packet_len);
    }

    // 接受包继续处理
    return nfq_set_verdict(qh, ntohl(ph->packet_id), NF_ACCEPT, 0, NULL);
}

// NF-Queue 初始化
static int init_nfqueue(void)
{
    struct nfq_handle *h;
    struct nfq_q_handle *qh;
    int fd, rv;
    char buf[4096];

    h = nfq_open();
    if (!h) return -1;

    if (nfq_unbind_pf(h, AF_INET) < 0) {
        nfq_close(h);
        return -1;
    }

    if (nfq_bind_pf(h, AF_INET) < 0) {
        nfq_close(h);
        return -1;
    }

    qh = nfq_create_queue(h, 32, &mdns_packet_callback, NULL);
    if (!qh) {
        nfq_close(h);
        return -1;
    }

    if (nfq_set_mode(qh, NFQNL_COPY_PACKET, 0xffff) < 0) {
        nfq_destroy_queue(qh);
        nfq_close(h);
        return -1;
    }

    fd = nfq_fd(h);

    // 主处理循环
    while ((rv = recv(fd, buf, sizeof(buf), 0)) && rv >= 0) {
        nfq_handle_packet(h, buf, rv);
    }

    nfq_destroy_queue(qh);
    nfq_close(h);
    return 0;
}
```

### 8.4 容器运行时集成

#### crun 配置和集成
```json
// /etc/lcm/runtime.json
{
  "ociVersion": "1.0.0",
  "runtime": {
    "path": "/usr/bin/crun",
    "runtimeArgs": [
      "--systemd-cgroup"
    ]
  },
  "storage": {
    "driver": "overlay2",
    "runRoot": "/run/containers/storage",
    "graphRoot": "/var/lib/containers/storage"
  },
  "network": {
    "networkDir": "/etc/cni/net.d",
    "pluginDirs": ["/usr/libexec/cni"]
  }
}
```

#### 容器生命周期管理
```c
// container_lifecycle.c
#include <lxc/lxccontainer.h>

// 容器创建
static int create_nokia_container(const char *name, const char *image_path)
{
    struct lxc_container *c;

    c = lxc_container_new(name, NULL);
    if (!c) return -1;

    // 设置容器配置
    c->set_config_item(c, "lxc.rootfs.path", image_path);
    c->set_config_item(c, "lxc.net.0.type", "veth");
    c->set_config_item(c, "lxc.net.0.link", "lcm0");
    c->set_config_item(c, "lxc.net.0.flags", "up");

    // Nokia 应用特殊配置
    c->set_config_item(c, "lxc.mount.entry",
                      "/var/run/ubus.sock opt/ubus.sock none bind,create=file");
    c->set_config_item(c, "lxc.cap.drop", "");  // 保持所有权限
    c->set_config_item(c, "lxc.apparmor.profile", "unconfined");

    if (!c->create(c, "download", NULL, NULL, LXC_CREATE_QUIET, NULL)) {
        lxc_container_put(c);
        return -1;
    }

    lxc_container_put(c);
    return 0;
}

// 容器启动
static int start_nokia_container(const char *name)
{
    struct lxc_container *c;

    c = lxc_container_new(name, NULL);
    if (!c) return -1;

    if (!c->start(c, 0, NULL)) {
        lxc_container_put(c);
        return -1;
    }

    lxc_container_put(c);
    return 0;
}
```

这些详细的技术实现展示了在 OpenWrt 21.02 上移植 prplLCM 和集成 Nokia 应用的具体工作内容和复杂度。
