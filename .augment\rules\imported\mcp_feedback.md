---
type: "manual"
---

1. During any process, task, or conversation, whether asking, replying, or completing phased tasks, you must call MCP tool mcp-feedback-enhanced to ask for feedback.
2. Unless receiving termination instructions, all steps must repeatedly call mcp-feedback-enhanced .
3. Whenever user feedback is received, if the feedback content is not empty, you must call mcp-feedback-enhanced again and adjust behavior based on the feedback content.
4. Only when the user explicitly indicates "end" or "no more interaction needed" can you stop calling mcp-feedback-enhanced , and the process is considered complete.