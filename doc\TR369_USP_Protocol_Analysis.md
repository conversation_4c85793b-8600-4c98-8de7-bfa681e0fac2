# TR-369 USP协议完整交互过程分析

## 概述

TR-369定义了用户服务平台（User Services Platform, USP）协议，这是一个现代化的设备管理协议，用于替代和改进TR-069。USP协议基于Protocol Buffers编码，支持多种传输协议，提供了更灵活、安全和高效的设备管理能力。

## USP协议架构

### 核心组件

1. **USP Agent** - 被管理设备上的USP客户端
2. **USP Controller** - 管理服务器，发送命令和接收通知
3. **USP Endpoint** - USP通信的端点，可以是Agent或Controller
4. **Message Transfer Protocol (MTP)** - 底层传输协议

### 协议栈结构

```
+------------------+
|   USP Messages   |  <- 应用层消息
+------------------+
|  Protocol Buffers|  <- 编码层
+------------------+
|       MTP        |  <- 传输层 (WebSocket, STOMP, MQTT, CoAP等)
+------------------+
|    Transport     |  <- 网络传输层 (TCP, UDP, TLS等)
+------------------+
```

## USP消息类型

### 1. Request消息类型

- **Get** - 获取数据模型参数值
- **Set** - 设置数据模型参数值
- **Add** - 添加对象实例
- **Delete** - 删除对象实例
- **Operate** - 执行操作命令
- **GetSupportedDM** - 获取支持的数据模型
- **GetInstances** - 获取对象实例
- **Notify** - 通知消息（Agent主动发送）
- **GetSupportedProtocol** - 获取支持的协议版本

### 2. Response消息类型

- **GetResp** - Get请求的响应
- **SetResp** - Set请求的响应
- **AddResp** - Add请求的响应
- **DeleteResp** - Delete请求的响应
- **OperateResp** - Operate请求的响应
- **GetSupportedDMResp** - GetSupportedDM的响应
- **GetInstancesResp** - GetInstances的响应
- **NotifyResp** - Notify的响应确认
- **GetSupportedProtocolResp** - GetSupportedProtocol的响应

### 3. Error消息类型

- **Error** - 错误响应消息

## USP消息结构

### 消息头部（Header）

```protobuf
message Header {
    string msg_id = 1;          // 消息唯一标识符
    string msg_type = 2;        // 消息类型
}
```

### 消息体（Body）

```protobuf
message Msg {
    Header header = 1;          // 消息头部
    Body body = 2;              // 消息体
}

message Body {
    oneof msg_body {
        Request request = 1;     // 请求消息
        Response response = 2;   // 响应消息
        Error error = 3;         // 错误消息
    }
}
```

## USP协议完整生命周期流程

### 阶段1：设备引导和发现（Bootstrap & Discovery）

#### 1.1 设备初始化和网络连接
```
Device Boot Process:
1. 硬件初始化
2. 网络接口配置
3. 获取IP地址（DHCP/Static）
4. DNS解析
5. 时间同步（NTP）
```

#### 1.2 Controller发现机制
设备可以通过多种方式发现Controller：

**方式1：预配置Controller信息**
```
Device Configuration:
- Controller URL: https://controller.example.com:443/usp
- Controller Certificate
- Device Certificate
- Shared Secret/Key
```

**方式2：DHCP选项发现**
```
DHCP Response:
Option 43 (Vendor-Specific): Controller URL
Option 125 (Vendor-Specific): USP Controller Info
```

**方式3：DNS-SD服务发现**
```
DNS Query: _usp._tcp.local
Response: controller.example.com:443
```

### 阶段2：设备入网（Onboarding）

#### 2.1 初始连接建立
```
Device (Agent)                Controller
    |                          |
    |--- TCP/TLS Handshake --->|  建立安全连接
    |<-- TLS Certificate ------|  验证Controller身份
    |--- Client Certificate -->|  提供设备身份
    |<-- Connection Established|  连接建立成功
    |                          |
```

#### 2.2 USP协议版本协商
```
Device (Agent)                Controller
    |                          |
    |--- GetSupportedProtocol->|  查询支持的协议版本
    |<-- GetSupportedProtocolResp|  返回版本信息
    |                          |
```

**GetSupportedProtocol请求示例：**
```protobuf
Header {
    msg_id: "agent-bootstrap-001"
    msg_type: "REQUEST"
}
Body {
    request {
        get_supported_protocol {
            controller_supported_protocol_versions: "1.1"
        }
    }
}
```

**GetSupportedProtocolResp响应示例：**
```protobuf
Header {
    msg_id: "agent-bootstrap-001"
    msg_type: "RESPONSE"
}
Body {
    response {
        get_supported_protocol_resp {
            agent_supported_protocol_versions: "1.1"
        }
    }
}
```

#### 2.3 设备认证和授权
```
Device (Agent)                Controller
    |                          |
    |--- Device Identity ----->|  发送设备标识信息
    |                          |--- 验证设备合法性
    |                          |--- 检查设备授权
    |<-- Authentication Result-|  返回认证结果
    |                          |
```

### 阶段3：设备注册（Registration）

#### 3.1 设备信息收集
```
Controller                    Device (Agent)
    |                          |
    |--- Get DeviceInfo ------>|  获取设备基本信息
    |<-- DeviceInfo Response --|  返回设备信息
    |                          |
    |--- GetSupportedDM ------>|  获取支持的数据模型
    |<-- SupportedDM Response -|  返回数据模型信息
    |                          |
    |--- GetInstances -------->|  获取对象实例
    |<-- Instances Response ---|  返回实例信息
    |                          |
```

**获取设备信息示例：**
```protobuf
Header {
    msg_id: "ctrl-register-001"
    msg_type: "REQUEST"
}
Body {
    request {
        get {
            param_paths: [
                "Device.DeviceInfo.Manufacturer",
                "Device.DeviceInfo.ModelName",
                "Device.DeviceInfo.SerialNumber",
                "Device.DeviceInfo.HardwareVersion",
                "Device.DeviceInfo.SoftwareVersion",
                "Device.DeviceInfo.ProductClass"
            ]
        }
    }
}
```

#### 3.2 设备配置初始化
```
Controller                    Device (Agent)
    |                          |
    |--- Set Initial Config -->|  设置初始配置
    |<-- Config Response ------|  确认配置结果
    |                          |
    |--- Add Subscriptions --->|  添加事件订阅
    |<-- Subscription Response-|  确认订阅设置
    |                          |
```

### 阶段4：正常运行管理（Normal Operation）

#### 4.1 参数读取操作
```
Controller                    Device (Agent)
    |                          |
    |--- Get Request --------->|  请求读取参数
    |                          |--- 验证访问权限
    |                          |--- 读取参数值
    |<-- Get Response ---------|  返回参数值
    |                          |
```

**Get请求示例：**
```protobuf
Header {
    msg_id: "ctrl-get-001"
    msg_type: "REQUEST"
}
Body {
    request {
        get {
            param_paths: [
                "Device.WiFi.Radio.1.Enable",
                "Device.WiFi.Radio.1.Channel",
                "Device.WiFi.Radio.1.TransmitPower"
            ]
        }
    }
}
```

#### 4.2 参数设置操作
```
Controller                    Device (Agent)
    |                          |
    |--- Set Request --------->|  请求设置参数
    |                          |--- 验证访问权限
    |                          |--- 验证参数值
    |                          |--- 应用配置
    |                          |--- 保存配置
    |<-- Set Response ---------|  返回设置结果
    |                          |
```

**Set请求示例：**
```protobuf
Header {
    msg_id: "ctrl-set-001"
    msg_type: "REQUEST"
}
Body {
    request {
        set {
            allow_partial: false
            update_objs {
                obj_path: "Device.WiFi.Radio.1."
                param_settings {
                    param: "Channel"
                    value: "6"
                    required: true
                }
                param_settings {
                    param: "TransmitPower"
                    value: "100"
                    required: true
                }
            }
        }
    }
}
```

#### 4.3 对象管理操作

**添加对象实例：**
```
Controller                    Device (Agent)
    |                          |
    |--- Add Request --------->|  请求添加对象
    |                          |--- 验证权限
    |                          |--- 创建对象实例
    |                          |--- 设置初始参数
    |<-- Add Response ---------|  返回新对象路径
    |                          |
```

**Add请求示例（添加WiFi接入点）：**
```protobuf
Header {
    msg_id: "ctrl-add-001"
    msg_type: "REQUEST"
}
Body {
    request {
        add {
            allow_partial: false
            create_objs {
                obj_path: "Device.WiFi.AccessPoint."
                param_settings {
                    param: "SSID"
                    value: "MyNetwork"
                    required: true
                }
                param_settings {
                    param: "Enable"
                    value: "true"
                    required: true
                }
            }
        }
    }
}
```

**删除对象实例：**
```
Controller                    Device (Agent)
    |                          |
    |--- Delete Request ------>|  请求删除对象
    |                          |--- 验证权限
    |                          |--- 删除对象实例
    |<-- Delete Response ------|  返回删除结果
    |                          |
```

#### 4.4 操作命令执行

**同步操作：**
```
Controller                    Device (Agent)
    |                          |
    |--- Operate Request ----->|  请求执行操作
    |                          |--- 验证权限
    |                          |--- 执行操作
    |<-- Operate Response -----|  返回执行结果
    |                          |
```

**异步操作：**
```
Controller                    Device (Agent)
    |                          |
    |--- Operate Request ----->|  请求执行操作
    |                          |--- 验证权限
    |                          |--- 启动异步操作
    |<-- Operate Response -----|  返回操作ID
    |                          |
    |                          |--- (异步操作执行中)
    |                          |--- (操作完成)
    |<-- Notify --------------|  通知操作完成
    |--- NotifyResp ---------->|  确认通知
    |                          |
```

**Operate请求示例（WiFi扫描）：**
```protobuf
Header {
    msg_id: "ctrl-operate-001"
    msg_type: "REQUEST"
}
Body {
    request {
        operate {
            command: "Device.WiFi.Radio.1.Scan()"
            command_key: "scan-001"
            send_resp: true
        }
    }
}
```

### 阶段5：事件通知和订阅（Event Notification）

#### 5.1 订阅设置
```
Controller                    Device (Agent)
    |                          |
    |--- Add Subscription ---->|  添加事件订阅
    |<-- Subscription Response-|  确认订阅
    |                          |
```

**订阅设置示例：**
```protobuf
Header {
    msg_id: "ctrl-subscribe-001"
    msg_type: "REQUEST"
}
Body {
    request {
        add {
            allow_partial: false
            create_objs {
                obj_path: "Device.LocalAgent.Subscription."
                param_settings {
                    param: "Enable"
                    value: "true"
                    required: true
                }
                param_settings {
                    param: "ID"
                    value: "wifi-events"
                    required: true
                }
                param_settings {
                    param: "NotifType"
                    value: "Event"
                    required: true
                }
                param_settings {
                    param: "ReferenceList"
                    value: "Device.WiFi.Radio.1."
                    required: true
                }
            }
        }
    }
}
```

#### 5.2 事件通知
```
Device (Agent)                Controller
    |                          |
    |--- Event Triggered ------|  事件发生
    |--- Notify Message ------>|  发送通知
    |<-- NotifyResp -----------|  确认接收
    |                          |
```

**事件通知示例：**
```protobuf
Header {
    msg_id: "agent-notify-001"
    msg_type: "NOTIFY"
}
Body {
    request {
        notify {
            subscription_id: "wifi-events"
            send_resp: true
            event {
                obj_path: "Device.WiFi.Radio.1."
                event_name: "StatusChange"
                params {
                    key: "Status"
                    value: "Up"
                }
                params {
                    key: "LastChange"
                    value: "2024-01-15T10:30:00Z"
                }
            }
        }
    }
}
```

### 阶段6：批量数据收集（Bulk Data Collection）

#### 6.1 批量数据配置
```
Controller                    Device (Agent)
    |                          |
    |--- Configure BDC ------->|  配置批量数据收集
    |<-- BDC Config Response --|  确认配置
    |                          |
```

**批量数据收集配置示例：**
```protobuf
Header {
    msg_id: "ctrl-bdc-001"
    msg_type: "REQUEST"
}
Body {
    request {
        add {
            allow_partial: false
            create_objs {
                obj_path: "Device.BulkData.Profile."
                param_settings {
                    param: "Enable"
                    value: "true"
                    required: true
                }
                param_settings {
                    param: "Name"
                    value: "WiFiStats"
                    required: true
                }
                param_settings {
                    param: "ReportingInterval"
                    value: "300"  // 5分钟
                    required: true
                }
                param_settings {
                    param: "Parameter"
                    value: "Device.WiFi.Radio.1.Stats."
                    required: true
                }
            }
        }
    }
}
```

#### 6.2 批量数据上报
```
Device (Agent)                Controller
    |                          |
    |--- Scheduled Collection -|  定时收集数据
    |--- Bulk Data Report ---->|  发送批量数据
    |<-- Report Acknowledgment-|  确认接收
    |                          |
```

### 阶段7：设备维护和更新（Maintenance & Update）

#### 7.1 固件更新
```
Controller                    Device (Agent)
    |                          |
    |--- Download Command ---->|  下载固件命令
    |<-- Download Response ----|  开始下载
    |                          |--- 下载固件
    |<-- Progress Notify ------|  下载进度通知
    |--- Install Command ----->|  安装固件命令
    |<-- Install Response -----|  开始安装
    |                          |--- 安装并重启
    |<-- Boot Notify ----------|  重启完成通知
    |                          |
```

#### 7.2 配置备份和恢复
```
Controller                    Device (Agent)
    |                          |
    |--- Backup Command ------>|  备份配置命令
    |<-- Backup Data ----------|  返回配置数据
    |                          |
    |--- Restore Command ----->|  恢复配置命令
    |<-- Restore Response -----|  确认恢复结果
    |                          |
```

### 阶段8：设备下线（Decommissioning）

#### 8.1 正常下线
```
Controller                    Device (Agent)
    |                          |
    |--- Disable Device ------>|  禁用设备
    |<-- Disable Response -----|  确认禁用
    |                          |
    |--- Remove Subscriptions->|  移除订阅
    |<-- Remove Response ------|  确认移除
    |                          |
    |--- Disconnect ---------->|  断开连接
    |                          |
```

#### 8.2 异常下线处理
```
Controller                    Device (Agent)
    |                          |
    |--- Connection Lost ------|  连接丢失
    |--- Retry Connection ---->|  重试连接
    |                          |
    |--- Timeout Exceeded -----|  超时
    |--- Mark Device Offline --|  标记设备离线
    |                          |
```

## 错误处理机制

### 错误响应格式
```protobuf
Header {
    msg_id: "ctrl-005"
    msg_type: "ERROR"
}
Body {
    error {
        err_code: 7004
        err_msg: "Invalid arguments"
        param_errs {
            param_path: "Device.WiFi.Radio.1.Channel"
            err_code: 7009
            err_msg: "Invalid value"
        }
    }
}
```

### 常见错误代码
- **7000** - Message failed
- **7001** - Message not supported
- **7002** - Request denied
- **7003** - Internal error
- **7004** - Invalid arguments
- **7005** - Resources exceeded
- **7006** - Permission denied
- **7007** - Invalid configuration
- **7008** - Invalid path name
- **7009** - Invalid parameter value

## 安全机制

### 1. 传输层安全
- TLS加密传输
- 证书验证
- 双向认证

### 2. 消息层安全
- 消息签名
- 消息加密
- 重放攻击防护

### 3. 访问控制
- 基于角色的访问控制（RBAC）
- 参数级权限控制
- 操作权限验证

## 传输协议支持

### 1. WebSocket
- 实时双向通信
- 低延迟
- 适合实时控制场景
- 基于TCP，支持TLS加密

### 2. STOMP (Simple Text Oriented Messaging Protocol)
- 基于消息队列
- 支持发布/订阅模式
- 适合大规模部署
- 可以运行在WebSocket或TCP之上

### 3. MQTT
- 轻量级消息协议
- 支持QoS
- 适合IoT设备
- 基于TCP，支持TLS加密

### 4. CoAP (Constrained Application Protocol)
- 适合资源受限设备
- UDP传输
- 支持观察者模式
- 支持DTLS加密

### 5. HTTP/HTTPS (USP over HTTP)
**重要说明：TR-369标准确实支持HTTP/HTTPS作为MTP传输协议**

#### HTTP MTP特性：
- **请求-响应模式**：适合同步操作
- **RESTful风格**：使用HTTP POST方法传输USP消息
- **HTTPS支持**：提供TLS加密和认证
- **防火墙友好**：使用标准HTTP端口（80/443）
- **负载均衡**：可以使用标准HTTP负载均衡器

#### HTTP MTP消息格式：
```
POST /usp HTTP/1.1
Host: controller.example.com
Content-Type: application/vnd.bbf.usp.msg
Content-Length: [message_length]
Authorization: Bearer [token]

[Protocol Buffer encoded USP message]
```

#### HTTP MTP适用场景：
- **批量数据收集**：适合定期收集设备状态和统计信息
- **配置管理**：适合设备配置的读取和设置
- **企业环境**：利用现有HTTP基础设施
- **云端集成**：易于与云服务集成

#### HTTP vs 其他MTP的比较：
| 特性 | HTTP/HTTPS | WebSocket | MQTT | STOMP |
|------|------------|-----------|------|-------|
| 实时性 | 中等 | 高 | 高 | 高 |
| 双向通信 | 需要轮询 | 原生支持 | 原生支持 | 原生支持 |
| 防火墙穿透 | 优秀 | 良好 | 一般 | 一般 |
| 资源消耗 | 中等 | 低 | 低 | 中等 |
| 批量操作 | 优秀 | 良好 | 良好 | 良好 |

## 与TR-069的主要区别

| 特性 | TR-069 | TR-369 (USP) |
|------|--------|--------------|
| 编码格式 | SOAP/XML | Protocol Buffers |
| 传输协议 | HTTP/HTTPS | WebSocket/STOMP/MQTT/CoAP/HTTP(S) |
| 通信模式 | 请求-响应 | 请求-响应 + 异步通知 |
| 消息大小 | 较大 | 较小 |
| 解析效率 | 较低 | 较高 |
| 扩展性 | 有限 | 良好 |
| 多控制器 | 不支持 | 支持 |

## 总结

USP协议通过现代化的设计理念，提供了比TR-069更加灵活、高效和安全的设备管理能力。其基于Protocol Buffers的编码方式、多传输协议支持、异步通知机制和增强的安全特性，使其更适合现代IoT和智能家居设备的管理需求。

协议的完整交互过程包括连接建立、能力协商、参数操作、对象管理、命令执行和事件通知等多个阶段，每个阶段都有明确的消息格式和处理流程，确保了设备管理的可靠性和一致性。

## 实现建议

### 针对WiFi设备的USP实现

根据您的需求"支持通过MQTT和HTTPS进行批量数据收集"，建议实现方案：

#### 1. MTP选择建议
- **HTTPS MTP**：用于批量数据收集和配置管理
  - 适合定期上报设备状态、统计信息
  - 利用现有HTTP基础设施
  - 易于与云平台集成

- **MQTT MTP**：用于实时事件通知
  - 适合WiFi连接状态变化通知
  - 支持QoS保证消息可靠性
  - 轻量级，适合频繁的小消息

#### 2. 实现架构
```
WiFi Device (USP Agent)
├── USP Core Engine
├── Data Model (Device.WiFi.*)
├── MTP Layer
│   ├── HTTPS Client
│   └── MQTT Client
└── Security Layer (TLS/DTLS)
```

#### 3. 批量数据收集实现
- 使用HTTPS MTP定期发送Get请求收集：
  - WiFi接入点状态
  - 客户端连接信息
  - 信号强度统计
  - 流量统计数据
  - 错误计数器

#### 4. 开发优先级
1. **第一阶段**：实现HTTPS MTP + 基础USP消息处理
2. **第二阶段**：实现MQTT MTP + 事件通知
3. **第三阶段**：完善安全机制和错误处理
