# Nokia Application Integration in 3rd Party

![](images/2dbc21cc8e9ad6c90cc4afbe49ca8527e957cb426a536aa740af98f040f4bba1.jpg)

$\circledcirc$ Nokia 2024. All rights reserved.

# About Nokia

At Nokia, we create technology that helps the world act together.

As a B2B technology innovation leader, we are pioneering networks that sense, think and act by leveraging our work across mobile, fixed and cloud networks. In addition, we create value with intellectual property and long-term research, led by the award-winning Nokia Bell Labs.

Service providers, enterprises and partners worldwide trust Nokia to deliver secure, reliable and sustainable networks today – and work with us to create the digital services and applications of the future.

# Contents

1 Overview .. 4   
2 Key Requirements for Integration . 5   
3 Integration Steps.. 6   
Application Requirements. 6   
Application Image Generation .. 6   
Application Integration . 7   
Validation and Release. 7   
4 Program Structure for Managing Integration . 8   
5 Support and Resources .. 9   
6 Appendix-A: Enabling Nokia Fingerprint Application on 3rd Party Devices ..... 10   
Resource Requirement. .. 10   
Host Data Model Requirement. 10   
Host Data Access Requirements. . 12   
Other Requirements .. 13

# 1 Overview

This application note outlines the process for integrating Nokia applications into thirdparty devices, ensuring seamless compatibility, optimal performance, and effective communication. Nokia’s advanced technologies, such as deep packet inspection (DPI), quality of service (QoS) management, and network optimization, can be integrated into a wide range of devices to enhance their functionality.

# 2 Key Requirements for Integration

To successfully integrate Nokia applications into a third-party device, the following requirements must be met:

Supported Operating System: The device must implement OCI compliant container lifecycle management and support of deployment, management, and updates of Nokia applications. Based on different application various kernel flags or modules might require to be enabled. prplLCM is OCI compliant Opensource container lifecycle management implemented in Nokia devices. The current version implemented is 3.1.0. Refer to Section-4 for more details on further reference to prplLCM.   
• Critical Resource Requirements: The system should support sufficient memory and CPU capacity to run the specified application, the memory, flash and CPU requirements vary from application to application. API and Protocol Support: The third-party device must support API interfaces compatible with HL-API specifications from prpl or BBF (Broadband Forum), ensuring consistent communication with Nokia applications.   
Privileged requirements: Per application might be needed and will be defined per application level. For example, shared network or other mount points.

# 3 Integration Steps

Integration steps for Nokia application in OEM devices are shown in the Figure 1.

![](images/045e1463fc36d661019b201e84d09aafbcbf50a24d5da66ef247e24926e5b60c.jpg)  
Figure 1: Application Integration Flow

# Application Requirements

In addition to the generic requirements for interfacing the application with the host (e.g., data-model access), each application may have specific requirements. These include:

1. Data-Model Requirements: Application-specific data-models that need to be implemented on the host.

2. Resource Requirements: Unique resource needs such as processor architecture, RAM, and storage.

For instance, details for enabling the Nokia Fingerprint Application are provided in Appendix-A: Enabling Nokia Fingerprint Application on 3rd Party Devices.

# Application Image Generation

The official and default application image generation is via Corteca Development Toolkit. While it is designed to meet the requirements of most OEM devices. However, in rare cases, an application image may need to be generated using the specific toolchain utilized by the OEM. This requirement will be evaluated on a case-by-case basis.

# Application Integration

• Obtain Installation Files: Download the necessary Nokia application packages from the Nokia repository, that matches the device capability in terms of processor   
• Install Dependencies: Ensure that the third-party device includes all required dependencies for a particular application, such as Kernel modules, libraries, and drivers, necessary to run the specific applications. Configure Environment: Set up the environment on the device, ensuring the network interfaces support Nokia applications’ traffic and management needs. For most of the applications this step may not be needed. Additional configuration for licensing might be needed, storing licensing key, configuration of license server etc. Based on application and business requirements.

# Validation and Release

Functional Testing: Verify that Nokia applications are installed and operating properly on the third-party device. This includes testing API interactions and validating data flows.   
Performance Testing: Measure the application’s impact on the device's network performance, latency, and resource consumption (CPU/memory). Interoperability Testing: Ensure Nokia applications work seamlessly with other services and components within the third-party device, such as forwarding, management, etc.

On successful completion of the validation, the application image is released by Nokia to the CSP and/or OEM.

# 4 Program Structure for Managing Integration

To manage the integration process effectively, a program structure should be established to oversee and drive the engineering efforts. We recommend the following structure:

![](images/2a0008c2c1460c9377372d768f0aaea70753cc93fa2cbaa6bf84d101c30ef21f.jpg)  
Figure 2: Program Structure

• Program Manager: Appointed by Airtel to oversee the overall program.

Technical Project Manager (TPM) from Nokia: Responsible for coordinating all internal activities related to the integration process.

Technical Project Manager (TPM) from OEM: Assigned to drive OEM’s integration engineering activities.

This governance structure will facilitate:

1. Alignment of project status across stakeholders.   
2. Maintenance of the integration schedule.   
3. Efficient resolution of any open points.

# Support and Resources

For further technical documentation, API references, and additional support, please contact Nokia’s technical support team or access the Nokia Developer Portal.

Please refer to:

Corteca Development toolkit documentation • Corteca Development toolkit Github Page • PrplLCM white paper https://prplfoundation.org/wp-content/uploads/2021/02/prpl-LCMProposal-v1.0.A.pdf • Prpl LCM project in Gitlab https://gitlab.com/prpl-foundation/lcm

# 6 Appendix-A: Enabling Nokia Fingerprint Application on 3rd Party Devices

This section provides a detailed description of the host-side requirements to enable the Nokia fingerprint application on third-party devices. The requirements are categorized as follows:

1. Resource Requirement   
2. Host Data Model Requirements   
3. Host Data Access Requirements   
4. Other requirements   
5. Host Interface Summary

# Resource Requirement

• Processor Architecture: Aarch64, Armv7l   
• CPU usage: o This will be very minimal. RAM usage: o Minimum: 3 MB o Typical: 5-10 MB o Maximum (container cap): 20 MB

• Disk usage: Approximately 10.5MB

# Host Data Model Requirement

The device fingerprinting application requires the following data models to be available on the host:

# 1. Fing database coordinate

The application expects the "Fing" database details to be provided by the host. These coordinates are used to configure the application to access the “Fing”:

<table><tr><td>Parameter</td><td>Access</td><td>Type</td><td>Length</td><td>Remark</td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td></tr></table>

<table><tr><td>Device.X_ALU- COM_FPEngine.Server</td><td>RW</td><td>String</td><td>128</td><td>“Fing&#x27;”server URL</td></tr><tr><td>Device.X_ALU-COM_FPEngine.Key</td><td>RW</td><td>String</td><td>512</td><td>Key for accessing the Fing server</td></tr></table>

Note: As an alternative, if security considerations allow, these coordinates can be hard coded within the application.

# 2. DeviceMap data-model

The DeviceMap data model is used by the application to store the results of the fingerprinting algorithm. This table is a proprietary data-model defined by Nokia. In this table, the expectation is that the host to create the entry with MACAddres, HostName and HostAlias based on the DHCP transaction. Subsequently the fingerprint application will fill the other fields. Please note, fingerprint application will not create or delete any table entries, that should be the responsibility of the host system.

<table><tr><td>Parameter</td><td>Access</td><td>Type</td><td>Length</td><td>Description</td></tr><tr><td>Device.X_ASB_LAN_DEVICE_MAP.DeviceMap.{i}. MACAddress</td><td>RW</td><td>String32</td><td></td><td>Part of standard datamodel</td></tr><tr><td>Device.X_ASB_LAN_DEVICE_MAP.DeviceMap.{i}. HostName</td><td>RW</td><td>String55</td><td></td><td>Part of standard datamodel</td></tr><tr><td>Device.X_ASB_LAN_DEvICE_MAP.DeviceMap.{i}. HostAlias (friendly name)</td><td>RW</td><td>String55</td><td></td><td>Part of standard datamodel</td></tr><tr><td>Device.X_ASB_LAN_DEvICE_MAP.DeviceMap.{i}. DeviceCategory</td><td>RW</td><td>String64</td><td></td><td>Extended to include the device category (e.g., Camera)</td></tr><tr><td>Device.X_ASB_LAN_DEVICE_MAP.DeviceMap.{i}. Manufacture</td><td>RW</td><td>String</td><td>64</td><td>Extended to include the manufacturer name</td></tr><tr><td>Device.X_ASB_LAN_DEvICE_MAP.DeviceMap.{i}. Model Name</td><td>RW</td><td>String64</td><td></td><td>Extended to include the model name</td></tr><tr><td>Device.X_ASB_LAN_DEVICE_MAP.DeviceMap.{i}. OS</td><td>RW</td><td>String32</td><td></td><td>Extended to include the Operating system name</td></tr></table>

<table><tr><td>Device.X_ASB_LAN_DEVICE_MAP.DeviceMap.{i}.</td><td>RW</td><td>Int</td><td>0-100</td><td>Extended to indicate the</td></tr><tr><td>ConfidenceLevel (Score)</td><td></td><td></td><td></td><td>confidence level (%)</td></tr></table>

Entries with a ConfidenceLevel above $60 \%$ are considered reliable.

# Host Data Access Requirements

The application accesses the host data model using various UBUS objects and methods associated with it. The application uses usp.raw UBUS object and get method to access the Device.X_ALUCOM_FPEngine data-model. The HostOS is required to expose the usp.raw object and get method. The sample execution, of the command, can be found here:

root@Beacon 19:/tmp# ubus -s /var/run/ubus-session/ubus.sock call usp.raw get   
'{"path":"Device.X_AIRTEL_FPEngine."}' "parameters": [ "parameter": "Device.X_AIRTEL_FPEngine.Key", "value": "" }, "parameter": "Device.X_AIRTEL_FPEngine.Server", "value": "" },

The Application expects the output of UBUS API in JSON formatted string as shown above. For updating fingerprint data in the DeviceMap table, the application executes the following UBUS command:

If the operation is successful, the host returns a fault value of 0. It is expected that the host system can provide the same interface, or else Nokia needs to adapt at the application level.

![](images/87a3e5bc8ec11dd76b41ffeb4ae4862e6ef2900757087a3e05d327d21e8e7b8e.jpg)

# Other Requirements

1. UBUS API: The host must implement UBUS APIs for accessing the required data models. The Host must mount UBUS socket to the container to access the UBUS object and methods. The Host must bind the Host UBUS socket to the container at /opt/ubus.sock path.

2. Interface Requirements: The application currently uses the br-lan interface for listening to traffic. If the host uses a different interface, Nokia will update the application accordingly.

3. Packet snooping: Host should ensure that DHCPv4 packets are received in the Linux application

4. NF-Queue Configuration: The application uses NF-Queue number 32 for listening to mDNS packets. If this number is unavailable, the host must provide an alternative, and Nokia will configure the application accordingly. Host is expected to ensure mDNS packets are received in this NF-queue.

5. USP Value change notification: The Corteca Cloud expects the USP implementation in the host to generate value change events for the DeviceMap table updates when configured by the USP manager (Corteca Cloud).

6. Logging: Host should mount a disk partition of size minimum 1MB for log storage.