---
type: "manual"
---

# C Language Programming Style and Rules

A good style is very important for code readability. This document summarizes our usual programming specifications.

Basicly, we need to follow the belowing rules:

- Avoid the use of unnecessary variables, macros, and similar elements.
- No useless files be posted.
- Remove or comment out any debug messages.
- Ensure that new code compiles without warnings.
- Prefer static memory allocation, if dynamic memory allocation is necessary, ensure to free the memory after use.
- Make sure to close files after they have been opened.
- Always verify the return values of function sscanf.
- Always use snprinf instead of any string operation function like sprintf, strcpy, strcat, strncpy etc.
- Always use strsep instead of strtok.
- Changes follow original file indent style, line ending style.
- For DM and restfulapi parameters, make sure backend API match the latest spec.
- No adding or removing empty lines from original file.
- No copy/paste code snippet, change code snippet exists more than once into functions.
- Newly written functions must include comments that describe the function, its parameters, and its return value. If the function is intended for public use, provide a simple usage example.
- Constant values should be defined using macros.

## File Naming Convention

All C files must end with `.c`, and header files must end with `.h`.

The filename should reflect the function of the module it contains as much as possible. Filenames must be all lowercase, start with a character, followed by characters, numbers, or underscores (`_`).

For example:

```c
foo.c foo.h
fpu_control.c fpu_control.h
```

## File Content Specification

Header files must follow the format below:

```c
#ifndef _EXAMPLE_H_
#define _EXAMPLE_H_

// ... header file body ...

#endif
```

*Note: Please replace `EXAMPLE` with the corresponding header filename.*

The "header file body" part in the format above can be organized in the following order:

- Standard headers or other headers to be included
- Macro definitions
- Struct definitions, including `typedef` and `enum`
- `static` variable and global variable declarations
- Function declarations

This order also applies to C files, with the function bodies coming last.

For more complex programs, a README file should be included to provide a clear design philosophy and usage instructions.

## Naming Convention for Variables, Constants, and Functions

The naming of variables is similar to file naming. They must be all lowercase, and abbreviations should be used to indicate intent as much as possible. Underscores should only be used when a single word is not clear enough.

Note that local variable names should be relatively short, while global variable names should be as meaningful as possible. Global variables and functions of the same functional module should have the same prefix.

Example:

```c
/* Local variables */
int i;    /* counter */
int ret;  /* return value */
```

```c
/* Global variables */
int g_firewall_rcv_packets;
int g_firewall_rules;
```

Here, the prefix `g_` indicates a global variable.

Macro names should use all uppercase English letters, numbers, and underscores.

Example:

```c
#define STDIN_FILENO    0
#define STDOUT_FILENO   1
#define STDERR_FILENO   2
```

Function names should consist of all lowercase letters, numbers, and underscores. Generally, the first word is a noun, representing the object being operated on or a module abbreviation; the last word is a verb, representing the action being performed.

Example:

```c
int block_write(int dev, long * pos, char * buf, int count);
int sys_ioctl(unsigned int fd, unsigned int cmd, unsigned long arg);
```

Here are some commonly used abbreviations for variable naming:

```bash
fops - file operations```

## Pointer and Struct Declaration Specification

When declaring a pointer, the `*` must be placed before the variable name.

Example:

```c
char *p, *q;
unsigned long *lptr;
```

Structs should be declared as shown below:

```c
struct example {
    int length;
    char data[];
};
```

For important structs, it is best to define them with `typedef` and add the suffix `_t` to the struct name.

```c
typedef struct example {
    int length;
    char data[];
} example_t;
```

## Indentation, Spaces, and Parentheses Usage Specification

Indentation must be 4 spaces.

The use of parentheses depends on personal preference, but the following points must be guaranteed:

- The opening and closing parentheses of a function must be at the beginning of the line.

Example:

```c
// Correct format:
int main()
{
    ...
}


// Incorrect format:
int main() {
    ...
}
```

To make the program clear and readable, please use spaces in the following situations.
Add a space on both sides of the following binary and ternary operators.

```bash
=
+
-
<
>
*
/
%
|
&
^
<=
>=
==
!=
?
:
```

But do not add a space after the following unary operators:

```bash
&
*
+
-
~
!
sizeof
```

For example:

```c
i = j + k;
i += j;
```

Add a space after control keywords like `if`, `for`, etc. For example:

```c
if (i > j)
for (i = 0; i < 100; i++)
```

There should be a space on both sides of the comment symbols.

```c
/* This is a right example */
/*This is a wrong example*/
```

There must be a space after separators like `,`.

```c
/* Well, this is a right example */
/* Well,this is a wrong example */
```

Proper use of blank lines can make the code clearer and more readable, therefore:

- There must be one blank line between functions.
- Do not leave blank lines between related code blocks, but it's best to separate functionally different code blocks with one blank line.

## Breaking long lines and strings

The limit on the length of lines is **80** columns and this is a strongly
preferred limit.

Commonly available tools can achive that easily.

- Emacs: Alt-q
- VsCode(with extension `Rewrap`): Alt-q

PS: reference to [Linux kernel coding style](https://www.kernel.org/doc/html/v5.6/process/coding-style.html)


## Comment

We uses Doxygen to generate API documentation. Typically, when a developer
writes code, specially formatted comment blocks are used in the `*.h` and `*.c`
files. Doxygen then parses these comment blocks and generates cross-referenced
HTML documents.

Reference to the following link for more details.

- https://www.doxygen.nl/manual/docblocks.html

JavaDoc-style comments are recommended.

### Documenting a Function

The following code is an example of how to write doxygen-compatible comments for
a function prototype:

```c

/**
 * A brief history of JavaDoc-style (C-style) comments.
 *
 * This is the typical JavaDoc-style C-style comment. It starts with two
 * asterisks.
 *
 * @param theory Even if there is only one possible unified theory. it is just a
 *               set of rules and equations.
 */
void cstyle( int theory );

/**
 * Register a network function for network address translation (NAT).
 *
 * This function registers a network function for NAT with the specified network
 * and hook operations. The network function will be called when packets pass
 * through the specified hook.
 *
 * @param net (IN)     The network namespace in which to register the NAT
 *                     function.
 *
 * @param ops (IN/OUT) The hook operations for the NAT function.
 *
 * @return 0 on success, or a negative error code on failure.
 */
int nf_nat_register_fn(struct net *net, const struct nf_hook_ops *ops);

```

Note on line 1, the comment starts with `/**` instead of the usual `/*`. This
tells doxygen that this is a doxygen- compatible comment block. The comment
block should start with a one or two sentence summary of the purpose of the
function.

On line 2, there is an empty line. This tells doxygen that the summary has ended
and that the detailed description of the function will start on the next line.

Lines 3–5 provide the detailed description of the function. The detailed
description section can contain multiple sentences.

Lines 7–9 list the parameters of the function. Doxygen requires that each
parameter description begins with @param variable-name. For example, `@param
net`. In SERCOMM code, we also add (IN), (IN/OUT), or (OUT) after the variable
name to clearly specify whether the value is passed in, passed in and out, or
passed out. Finally, the description of the parameter. The description can span
multiple lines.

If the function has a return value, then a `@return description of the return
value` should be the last thing in the comment block.

### Documenting a Structure

The following code shows how to write doxygen-compatible comments for a
structure definition

```c
/**
 * A structure to keep track of instance information.
 *
 * External callers can treat this as an opaque handle. Note the instance array
 * must be of type unsigned int because the instance id's are constantly
 * increasing, so we cannot save space by defining instance to be an array of
 * unsigned char's.
 */
typedef struct
{
    unsigned char current_depth; /**< next index in the instance array to
                                  * fill. 0 means empty. */
    unsigned int instance[MAX_INSTANCE_DEPTH]; /**< Array of instance id's */
} instance_id_stack;
```

Just as with function prototypes, the comment block above a structure definition
should also begin with `/**`. The first sentence or two should be a brief summary
of the structure. A blank comment line (line 2) separates the brief description
from the detailed description (lines 3–6). The detailed description is optional.

Each of the fields of the structure should be documented using a comment line
that begins with `/**<`. The field comment can span multiple lines.

### Documenting a macro, #define, or typedef

The following is an example of how to write doxygen compatible comments for
typedef. The format is the same for macros and `#defines`.

```c
/**
 * A number to identify a mdm_object (but not the specific instance of the
 * object.
 *
 * mdm_object_id's are defined in mdm_oid.h
 */
typedef unsigned int mdm_object_id;
```

The comment block is the same as the function comment block. For macros, it may
be possible to also specify a `@param` in the comment block.

### Documenting an Enumeration

The following code shows how to write doxygen-compatible comments for an
enumeration.

```c
/*!\enum mdm_param_types
* \brief Possible types for parameters in the MDM.
*
* The first 6 (MPT_STRING through MPT_BASE64) are from the TR-098 spec.
* The next 3 (MPT_HEX_BINARY through MPT_UNSIGNED_LONG64) were introduced in
* TR-106 issue 1, Admendment 2, Sept 2008.
*/
typedef enum
{
    MPT_STRING = 0,            /**< string. */
    MPT_INTEGER = 1,           /**< Signed 32 bit integer */
    MPT_UNSIGNED_INTEGER = 2,  /**< Unsigned 32 bit integer */
    MPT_BOOLEAN = 3,           /**< Either 1 (true) or 0 (false). */
    MPT_DATE_TIME = 4,         /**< string, in UTC, in ISO 8601 date-format */
    MPT_BASE64 = 5,            /**< Base64 string representation of binary
                                * data. */
    MPT_HEX_BINARY = 6,        /**< Hex string representation of binary data */
    MPT_LONG64 = 7,            /**< Signed 64 bit integer */
    MPT_UNSIGNED_LONG64 = 8,   /**< Unsigned 64 bit integer */
} mdm_param_types;
```

Unlike the other doxygen-compatible comment blocks, the first line of the
comment block for an enumeration must begin with `/*!\enum
name-of-the-enum`. The second line contains the one sentence brief description
of the enum, which must be marked by the `\brief` keyword. The detailed
description section is like the comment block for functions. Finally, each
member of the enum should be commented with `/**<` , just like the fields in a
structure definition.


## Coding Style Tools

There are lots of pre-defined coding styles. (reference to
https://en.wikipedia.org/wiki/Indentation_style) We just use a variant linux
coding style for new sercomm source code, which change tab width from 8 to 4.

You can format your code with the following tools.

### clang-format

Save the following content in file .clang-format in your package's root
dir. Then run it with `clang-format`.

```bash
#
#
# clang-format configuration file. Intended for clang-format >= 11.
#
# This file is inherited from the Linux kernel format, only change the tab
# width from 8 to 4.
#
# For more information, see:
# - https://raw.githubusercontent.com/torvalds/linux/master/.clang-format
# - <kernel>/Documentation/process/coding-style.rst
#
---
AccessModifierOffset: -4
AlignAfterOpenBracket: Align
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignEscapedNewlines: Left
AlignOperands: true
AlignTrailingComments: false
AllowAllParametersOfDeclarationOnNextLine: false
AllowShortBlocksOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: false
AllowShortFunctionsOnASingleLine: None
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false
AlwaysBreakAfterDefinitionReturnType: None
AlwaysBreakAfterReturnType: None
AlwaysBreakBeforeMultilineStrings: false
AlwaysBreakTemplateDeclarations: false
BinPackArguments: true
BinPackParameters: true
BraceWrapping:
  AfterClass: false
  AfterControlStatement: false
  AfterEnum: false
  AfterFunction: true
  AfterNamespace: true
  AfterObjCDeclaration: false
  AfterStruct: false
  AfterUnion: false
  AfterExternBlock: false
  BeforeCatch: false
  BeforeElse: false
  IndentBraces: false
  SplitEmptyFunction: true
  SplitEmptyRecord: true
  SplitEmptyNamespace: true
BreakBeforeBinaryOperators: None
BreakBeforeBraces: Custom
BreakBeforeInheritanceComma: false
BreakBeforeTernaryOperators: false
BreakConstructorInitializersBeforeComma: false
BreakConstructorInitializers: BeforeComma
BreakAfterJavaFieldAnnotations: false
BreakStringLiterals: false
ColumnLimit: 80
CommentPragmas: '^ IWYU pragma:'
CompactNamespaces: false
ConstructorInitializerAllOnOneLineOrOnePerLine: false
ConstructorInitializerIndentWidth: 4
ContinuationIndentWidth: 4
Cpp11BracedListStyle: false
DerivePointerAlignment: false
DisableFormat: false
ExperimentalAutoDetectBinPacking: false
FixNamespaceComments: false
IncludeBlocks: Preserve
IncludeCategories:
  - Regex: '.*'
    Priority: 1
IncludeIsMainRegex: '(Test)?$'
IndentCaseLabels: false
IndentGotoLabels: false
IndentPPDirectives: None
IndentWidth: 4
IndentWrappedFunctionNames: false
JavaScriptQuotes: Leave
JavaScriptWrapImports: true
KeepEmptyLinesAtTheStartOfBlocks: false
MacroBlockBegin: ''
MacroBlockEnd: ''
MaxEmptyLinesToKeep: 1
NamespaceIndentation: None
ObjCBinPackProtocolList: Auto
ObjCBlockIndentWidth: 4
ObjCSpaceAfterProperty: true
ObjCSpaceBeforeProtocolList: true

# Taken from git's rules
PenaltyBreakAssignment: 10
PenaltyBreakBeforeFirstCallParameter: 30
PenaltyBreakComment: 10
PenaltyBreakFirstLessLess: 0
PenaltyBreakString: 10
PenaltyExcessCharacter: 100
PenaltyReturnTypeOnItsOwnLine: 60

PointerAlignment: Right
ReflowComments: false
SortIncludes: false
SortUsingDeclarations: false
SpaceAfterCStyleCast: false
SpaceAfterTemplateKeyword: true
SpaceBeforeAssignmentOperators: true
SpaceBeforeCtorInitializerColon: true
SpaceBeforeInheritanceColon: true
SpaceBeforeParens: ControlStatementsExceptForEachMacros
SpaceBeforeRangeBasedForLoopColon: true
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 1
SpacesInAngles: false
SpacesInContainerLiterals: false
SpacesInCStyleCastParentheses: false
SpacesInParentheses: false
SpacesInSquareBrackets: false
Standard: Cpp03
TabWidth: 4
UseTab: Always
...
```

reference

- [Creating and Enforcing a Code Formatting Standard with clang-format](https://embeddedartistry.com/blog/2017/10/23/creating-and-enforcing-a-code-formatting-standard-with-clang-format/)
