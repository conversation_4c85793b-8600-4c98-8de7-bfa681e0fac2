# OCI 标准和镜像格式详解

## 1. OCI 标准概述

### 1.1 什么是 OCI
**OCI (Open Container Initiative)** 是一个开放的容器标准组织，由 Linux Foundation 主导，制定了容器技术的行业标准。

### 1.2 OCI 三大核心规范
1. **OCI Image Specification**: 容器镜像格式标准
2. **OCI Runtime Specification**: 容器运行时标准  
3. **OCI Distribution Specification**: 容器镜像分发标准

## 2. OCI 镜像格式详解

### 2.1 OCI 镜像的本质
**重要概念**: OCI 镜像本身**不直接包含 rootfs**，而是包含**构建 rootfs 所需的层（layers）**。

OCI 镜像实际上是：
1. **分层的文件系统快照**: 每一层都是文件系统的增量变化
2. **元数据集合**: 描述如何组合这些层以及如何运行容器
3. **内容寻址存储**: 所有内容通过 SHA256 哈希值标识

### 2.2 OCI 镜像结构（实际文件布局）
```
OCI 镜像目录结构:
my-app-image/
├── oci-layout             # OCI 布局标识文件
├── index.json             # 镜像索引文件
└── blobs/                 # 所有数据的存储目录
    └── sha256/            # 按 SHA256 哈希组织
        ├── abc123...      # 镜像清单 (manifest)
        ├── def456...      # 镜像配置 (config)
        ├── layer1hash...  # 第1层文件系统 (tar.gz)
        ├── layer2hash...  # 第2层文件系统 (tar.gz)
        └── layer3hash...  # 第3层文件系统 (tar.gz)
```

### 2.3 层（Layer）的实际内容
每个层文件（如 `layer1hash...`）是一个 **tar.gz 压缩包**，包含：

**示例层内容**:
```bash
# 解压第一层可能包含：
/bin/
/bin/sh
/bin/ls
/etc/
/etc/passwd
/lib/
/lib/libc.so.6
...

# 解压第二层可能包含：
/usr/
/usr/bin/
/usr/bin/curl
/opt/
/opt/myapp/
/opt/myapp/binary
...

# 解压第三层可能包含：
/opt/myapp/config.json
/opt/myapp/data/
```

### 2.4 rootfs 是如何生成的？
**关键理解**: rootfs 是在**运行时**通过**叠加所有层**生成的：

```
运行时过程:
1. 容器运行时读取 OCI 镜像
2. 按顺序解压所有层
3. 使用联合文件系统（如 overlay2）将层叠加
4. 生成最终的 rootfs 目录
5. 在 rootfs 中启动容器进程
```

**实际叠加过程**:
```
Layer 1: /bin/sh, /etc/passwd, /lib/libc.so.6
Layer 2: /usr/bin/curl, /opt/myapp/binary
Layer 3: /opt/myapp/config.json

最终 rootfs:
/
├── bin/
│   └── sh                 # 来自 Layer 1
├── etc/
│   └── passwd             # 来自 Layer 1
├── lib/
│   └── libc.so.6          # 来自 Layer 1
├── usr/
│   └── bin/
│       └── curl           # 来自 Layer 2
└── opt/
    └── myapp/
        ├── binary         # 来自 Layer 2
        ├── config.json    # 来自 Layer 3
        └── data/          # 来自 Layer 3
```

### 2.5 实际查看 OCI 镜像内容的例子

让我们用一个真实的例子来看 OCI 镜像的内容：

```bash
# 下载一个简单的镜像到 OCI 格式
skopeo copy docker://alpine:latest oci:alpine-oci:latest

# 查看 OCI 镜像目录结构
ls -la alpine-oci/
# 输出:
# drwxr-xr-x  3 <USER> <GROUP>   96 Jan  1 12:00 blobs
# -rw-r--r--  1 <USER> <GROUP>   32 Jan  1 12:00 index.json
# -rw-r--r--  1 <USER> <GROUP>   30 Jan  1 12:00 oci-layout

# 查看 blobs 目录
ls -la alpine-oci/blobs/sha256/
# 输出:
# -rw-r--r--  1 <USER> <GROUP>  1234 Jan  1 12:00 a1b2c3d4...  # manifest
# -rw-r--r--  1 <USER> <GROUP>  5678 Jan  1 12:00 e5f6g7h8...  # config
# -rw-r--r--  1 <USER> <GROUP>  2.8M Jan  1 12:00 i9j0k1l2...  # layer (tar.gz)

# 解压查看层的内容
mkdir temp-layer
cd temp-layer
tar -xzf ../alpine-oci/blobs/sha256/i9j0k1l2...

# 查看层内容
ls -la
# 输出: 这就是 Alpine Linux 的文件系统内容
# drwxr-xr-x  2 <USER> <GROUP>  4096 Jan  1 12:00 bin
# drwxr-xr-x  2 <USER> <GROUP>  4096 Jan  1 12:00 etc
# drwxr-xr-x  2 <USER> <GROUP>  4096 Jan  1 12:00 lib
# drwxr-xr-x  2 <USER> <GROUP>  4096 Jan  1 12:00 usr
# ...

# 这些文件就是最终 rootfs 的内容！
```

### 2.6 Nokia 应用 OCI 镜像的可能结构

基于 Nokia 应用的要求，其 OCI 镜像可能包含以下层：

```
Nokia 应用镜像层结构:
Layer 1 (基础层):
├── bin/sh, /bin/bash
├── lib/libc.so.6, /lib/ld-linux.so.2
├── etc/passwd, /etc/group
└── usr/bin/基础工具

Layer 2 (应用层):
├── opt/nokia-app/
│   ├── bin/fingerprint-daemon
│   ├── lib/libfing.so
│   └── config/default.conf
├── usr/local/bin/helper-scripts
└── etc/init.d/nokia-app

Layer 3 (配置层):
├── opt/nokia-app/config/
│   ├── device-models.json
│   └── network-config.json
└── var/lib/nokia-app/
    └── database/
```

### 2.7 关键文件说明

#### oci-layout 文件
```json
{
  "imageLayoutVersion": "1.0.0"
}
```
- 标识这是一个 OCI 镜像布局
- 指定布局版本

#### index.json 文件
```json
{
  "schemaVersion": 2,
  "manifests": [
    {
      "mediaType": "application/vnd.oci.image.manifest.v1+json",
      "digest": "sha256:abc123...",
      "size": 1234,
      "annotations": {
        "org.opencontainers.image.ref.name": "latest"
      }
    }
  ]
}
```

#### manifest.json 文件
```json
{
  "schemaVersion": 2,
  "mediaType": "application/vnd.oci.image.manifest.v1+json",
  "config": {
    "mediaType": "application/vnd.oci.image.config.v1+json",
    "digest": "sha256:config-hash",
    "size": 1234
  },
  "layers": [
    {
      "mediaType": "application/vnd.oci.image.layer.v1.tar+gzip",
      "digest": "sha256:layer1-hash",
      "size": 5678
    },
    {
      "mediaType": "application/vnd.oci.image.layer.v1.tar+gzip", 
      "digest": "sha256:layer2-hash",
      "size": 9012
    }
  ]
}
```

#### config.json 文件
```json
{
  "architecture": "amd64",
  "os": "linux",
  "config": {
    "Env": [
      "PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
    ],
    "Cmd": ["/bin/sh"],
    "WorkingDir": "/",
    "ExposedPorts": {
      "80/tcp": {}
    }
  },
  "rootfs": {
    "type": "layers",
    "diff_ids": [
      "sha256:layer1-uncompressed-hash",
      "sha256:layer2-uncompressed-hash"
    ]
  },
  "history": [
    {
      "created": "2023-01-01T00:00:00Z",
      "created_by": "ADD file.tar /"
    }
  ]
}
```

## 3. OCI 镜像文件后缀和格式

### 3.1 常见的 OCI 镜像格式

#### 3.1.1 OCI Layout 目录格式
- **格式**: 目录结构
- **后缀**: 无特定后缀，通常是目录名
- **示例**: `my-app-image/` 目录

#### 3.1.2 OCI Archive 格式
- **格式**: tar 归档文件
- **后缀**: `.tar`, `.tar.gz`, `.tgz`
- **示例**: `my-app-image.tar`

#### 3.1.3 Docker Archive 格式（兼容 OCI）
- **格式**: Docker save 格式的 tar 文件
- **后缀**: `.tar`
- **示例**: `docker-image.tar`

### 3.2 Nokia 应用可能的镜像格式
基于 Nokia 应用集成指南，Nokia 应用镜像可能采用以下格式：
- **OCI Layout**: 解压的目录格式
- **OCI Archive**: `.tar` 或 `.tar.gz` 压缩格式
- **容器注册表**: 通过 HTTP API 分发

## 4. 容器运行时使用 OCI 镜像的例子

### 4.1 使用 crun 运行 OCI 镜像

#### 步骤 1: 准备 OCI 镜像
```bash
# 从 Docker 镜像转换为 OCI 格式
skopeo copy docker://alpine:latest oci:alpine-oci:latest

# 或者直接下载 OCI 格式镜像
skopeo copy docker://alpine:latest oci-archive:alpine.tar
```

#### 步骤 2: 从 OCI 镜像创建 rootfs
```bash
# 创建 bundle 目录
mkdir alpine-bundle
cd alpine-bundle

# 方法1: 使用 umoci 从 OCI 镜像创建 rootfs
umoci unpack --image ../alpine-oci:latest bundle

# 方法2: 手动创建 rootfs（理解原理）
mkdir rootfs

# 读取 OCI 镜像的 manifest，获取所有层
# 按顺序解压每一层到 rootfs
for layer in $(cat ../alpine-oci/blobs/sha256/manifest | jq -r '.layers[].digest' | cut -d: -f2); do
    tar -C rootfs -xzf ../alpine-oci/blobs/sha256/$layer
done

# 生成 OCI 运行时配置
crun spec

# 现在 rootfs 目录包含完整的文件系统
ls -la rootfs/
# 输出:
# drwxr-xr-x  2 <USER> <GROUP>  4096 Jan  1 12:00 bin
# drwxr-xr-x  2 <USER> <GROUP>  4096 Jan  1 12:00 etc
# drwxr-xr-x  2 <USER> <GROUP>  4096 Jan  1 12:00 lib
# drwxr-xr-x  2 <USER> <GROUP>  4096 Jan  1 12:00 usr
# ...
```

**重要理解**:
- OCI 镜像中的层文件是**压缩的 tar 包**
- rootfs 是通过**解压并叠加所有层**生成的
- 最终的 rootfs 就是容器的完整文件系统

#### 步骤 3: 修改 config.json
```json
{
  "ociVersion": "1.0.0",
  "process": {
    "terminal": true,
    "user": {
      "uid": 0,
      "gid": 0
    },
    "args": [
      "sh"
    ],
    "env": [
      "PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin",
      "TERM=xterm"
    ],
    "cwd": "/",
    "capabilities": {
      "bounding": ["CAP_AUDIT_WRITE", "CAP_KILL", "CAP_NET_BIND_SERVICE"],
      "effective": ["CAP_AUDIT_WRITE", "CAP_KILL", "CAP_NET_BIND_SERVICE"],
      "inheritable": ["CAP_AUDIT_WRITE", "CAP_KILL", "CAP_NET_BIND_SERVICE"],
      "permitted": ["CAP_AUDIT_WRITE", "CAP_KILL", "CAP_NET_BIND_SERVICE"]
    }
  },
  "root": {
    "path": "rootfs",
    "readonly": false
  },
  "hostname": "alpine-container",
  "mounts": [
    {
      "destination": "/proc",
      "type": "proc",
      "source": "proc"
    },
    {
      "destination": "/dev",
      "type": "tmpfs",
      "source": "tmpfs",
      "options": ["nosuid", "strictatime", "mode=755", "size=65536k"]
    }
  ],
  "linux": {
    "namespaces": [
      {"type": "pid"},
      {"type": "network"},
      {"type": "ipc"},
      {"type": "uts"},
      {"type": "mount"}
    ]
  }
}
```

#### 步骤 4: 运行容器
```bash
# 使用 crun 运行容器
crun run alpine-container

# 或者使用 runc
runc run alpine-container
```

### 4.2 使用 LXC 运行 OCI 镜像

#### 创建 LXC 容器配置
```bash
# 创建 LXC 配置文件
cat > alpine.conf << EOF
lxc.rootfs.path = dir:/path/to/alpine-bundle/rootfs
lxc.uts.name = alpine-lxc
lxc.net.0.type = veth
lxc.net.0.link = lxcbr0
lxc.net.0.flags = up
lxc.net.0.hwaddr = 00:16:3e:xx:xx:xx
EOF

# 创建并启动 LXC 容器
lxc-create -n alpine-container -f alpine.conf
lxc-start -n alpine-container
```

### 4.3 Nokia 应用容器运行示例

基于 Nokia 应用的特殊要求，运行配置可能如下：

```json
{
  "ociVersion": "1.0.0",
  "process": {
    "terminal": false,
    "user": {
      "uid": 0,
      "gid": 0
    },
    "args": [
      "/opt/nokia-app/bin/fingerprint-daemon"
    ],
    "env": [
      "PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin",
      "NOKIA_CONFIG_PATH=/opt/config"
    ],
    "cwd": "/opt/nokia-app"
  },
  "root": {
    "path": "rootfs",
    "readonly": false
  },
  "hostname": "nokia-fingerprint",
  "mounts": [
    {
      "destination": "/var/run/ubus.sock",
      "type": "bind",
      "source": "/var/run/ubus.sock",
      "options": ["bind", "rw"]
    },
    {
      "destination": "/opt/config",
      "type": "bind", 
      "source": "/etc/nokia-app",
      "options": ["bind", "ro"]
    }
  ],
  "linux": {
    "namespaces": [
      {"type": "pid"},
      {"type": "ipc"},
      {"type": "uts"},
      {"type": "mount"}
    ],
    "capabilities": {
      "bounding": ["CAP_NET_ADMIN", "CAP_NET_RAW", "CAP_SYS_ADMIN"],
      "effective": ["CAP_NET_ADMIN", "CAP_NET_RAW", "CAP_SYS_ADMIN"],
      "inheritable": ["CAP_NET_ADMIN", "CAP_NET_RAW", "CAP_SYS_ADMIN"],
      "permitted": ["CAP_NET_ADMIN", "CAP_NET_RAW", "CAP_SYS_ADMIN"]
    }
  }
}
```

## 5. OCI 镜像操作工具

### 5.1 镜像构建工具
- **buildah**: 构建 OCI 镜像
- **kaniko**: 在容器中构建镜像
- **img**: 无需 root 权限的镜像构建

### 5.2 镜像管理工具
- **skopeo**: 镜像复制、检查、签名
- **crane**: Google 开发的镜像工具
- **regctl**: 注册表客户端工具

### 5.3 运行时工具
- **crun**: 轻量级 OCI 运行时（C 语言）
- **runc**: 标准 OCI 运行时（Go 语言）
- **kata-runtime**: 基于虚拟机的安全运行时

## 6. 实际操作示例

### 6.1 创建简单的 OCI 镜像
```bash
# 使用 buildah 创建镜像
buildah from scratch
buildah copy working-container /bin/sh /bin/sh
buildah config --cmd /bin/sh working-container
buildah commit working-container my-simple-image

# 导出为 OCI 格式
buildah push my-simple-image oci:my-image:latest
```

### 6.2 检查 OCI 镜像
```bash
# 使用 skopeo 检查镜像
skopeo inspect oci:my-image:latest

# 查看镜像层信息
skopeo inspect --raw oci:my-image:latest | jq .
```

### 6.3 转换镜像格式
```bash
# Docker 格式转 OCI 格式
skopeo copy docker://alpine:latest oci:alpine-oci:latest

# OCI 格式转 Docker Archive
skopeo copy oci:alpine-oci:latest docker-archive:alpine.tar

# 上传到注册表
skopeo copy oci:alpine-oci:latest docker://registry.example.com/alpine:latest
```

## 7. 总结

### 7.1 OCI 标准的优势
1. **标准化**: 统一的容器镜像和运行时标准
2. **互操作性**: 不同工具和平台之间的兼容性
3. **安全性**: 内容寻址和签名验证
4. **可移植性**: 跨平台和跨环境部署

### 7.2 Nokia 应用集成要点
1. **镜像格式**: Nokia 应用使用标准 OCI 镜像格式
2. **运行时要求**: 需要特权容器和特定的挂载点
3. **网络配置**: 需要访问主机网络和 UBUS 套接字
4. **资源限制**: 合理配置 CPU 和内存限制

通过理解 OCI 标准和镜像格式，可以更好地集成和管理 Nokia 应用容器，确保在 OpenWrt 或 prplOS 系统上的正确运行。

## 8. 关键概念总结

### 8.1 OCI 镜像 vs rootfs 的关系

```
简单类比:
OCI 镜像 = 压缩包 + 说明书
rootfs = 解压后的完整文件系统

具体过程:
1. OCI 镜像存储: 多个 tar.gz 层文件 + 元数据
2. 容器运行时: 读取镜像，解压所有层
3. 文件系统叠加: 将所有层合并成一个目录树
4. 生成 rootfs: 完整的文件系统目录
5. 启动容器: 在 rootfs 中运行进程
```

### 8.2 实际文件对比

**OCI 镜像文件**:
```bash
alpine-oci/blobs/sha256/abc123...  # 2.8MB 的 tar.gz 文件
```

**解压后的 rootfs**:
```bash
rootfs/
├── bin/sh          # 实际的可执行文件
├── etc/passwd      # 实际的配置文件
├── lib/libc.so.6   # 实际的库文件
└── usr/bin/curl    # 实际的应用程序
```

### 8.3 Nokia 应用的实际部署流程

```
1. Nokia 提供 OCI 镜像文件 (如 nokia-app.tar)
   ├── 包含应用二进制文件的层
   ├── 包含配置文件的层
   └── 包含依赖库的层

2. prplLCM 或容器运行时处理:
   ├── 解压所有层到临时目录
   ├── 叠加生成完整的 rootfs
   └── 在 rootfs 中启动 Nokia 应用

3. 最终运行状态:
   ├── rootfs/opt/nokia-app/bin/fingerprint-daemon (运行中)
   ├── rootfs/opt/nokia-app/config/ (配置文件)
   └── 挂载的主机目录 (/var/run/ubus.sock)
```

**总结**: OCI 镜像是**分层存储的压缩格式**，rootfs 是**运行时生成的完整文件系统**。镜像本身不包含 rootfs，而是包含构建 rootfs 的所有材料。
