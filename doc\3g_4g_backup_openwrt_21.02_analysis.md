# 3G/4G备份功能在OpenWrt 21.02上的实现分析

## 概述

本文档深入分析3G/4G备份功能在**OpenWrt 21.02 (Linux Kernel 5.4.55)**上的实现现状、应用场景以及开发工作量估算。

## 目标平台规格

- **OpenWrt版本**: 21.02.x
- **Linux内核**: 5.4.55
- **发布时间**: 2021年8月
- **支持状态**: LTS (长期支持)

## 1. 3G/4G备份技术原理与应用示例

### 1.1 WAN备份技术概述

**WAN备份定义：**
WAN备份是指当主要互联网连接（如光纤、ADSL、以太网）出现故障时，自动切换到备用连接（3G/4G/5G移动网络）以保持网络连通性的技术。

**核心组件：**
- **主WAN连接**: 主要的互联网接入（光纤/ADSL/以太网）
- **备用WAN连接**: 3G/4G/5G移动网络连接
- **连接监控**: 检测主WAN连接状态
- **自动切换**: 故障时自动切换到备用连接
- **故障恢复**: 主连接恢复后自动切回

### 1.2 实际应用场景

#### 1.2.1 企业分支机构场景

**网络拓扑：**
```
总部 ---- 互联网 ---- [企业分支路由器] ---- 分支局域网
                           |
                      [4G USB调制解调器]
                           |
                      移动运营商网络
```

**应用示例：**
```bash
# 场景：银行分支机构网络
正常情况：
分支ATM机 -> 企业路由器 -> 专线 -> 总部数据中心

故障情况：
专线中断 -> 自动切换到4G -> ATM机继续工作
4G网络：分支ATM机 -> 企业路由器 -> 4G网络 -> 总部数据中心

# 业务连续性保障：
- ATM取款服务不中断
- POS机刷卡正常工作
- 分支办公网络保持连通
- 监控系统持续运行
```

#### 1.2.2 家庭办公场景

**家庭网络应用：**
```
家庭设备 ---- [OpenWrt路由器] ---- 光纤宽带
                    |
               [4G USB调制解调器]
                    |
               移动运营商网络

# 应用示例：
正常工作：在家视频会议，使用光纤宽带
光纤故障：自动切换4G，视频会议不中断
网络恢复：自动切回光纤，节省4G流量
```

#### 1.2.3 零售连锁店场景

**零售店网络部署：**
```bash
# 连锁便利店网络架构
收银系统 ---- [OpenWrt路由器] ---- 有线宽带 ---- 总部ERP系统
监控系统           |
WiFi客户           |
                [4G模块]
                   |
              移动运营商网络

# 实际应用价值：
- 收银系统24/7在线，支付不中断
- 库存管理实时同步
- 监控系统持续录像上传
- 客户WiFi服务不间断
- 总部可远程管理所有门店
```

#### 1.2.4 工业物联网场景

**工厂设备监控：**
```bash
# 工厂生产线监控网络
生产设备传感器 ---- [工业路由器] ---- 以太网 ---- 云平台
环境监控系统              |
安全系统                  |
                    [4G工业模块]
                         |
                   移动运营商网络

# 关键应用：
- 生产数据实时上传
- 设备故障及时报警
- 远程设备控制
- 安全监控不间断
```

### 1.3 3G/4G技术原理

#### 1.3.1 移动网络接入技术

**3G技术特点：**
- **UMTS/HSPA**: 下行最高42Mbps，上行最高5.76Mbps
- **CDMA2000**: 下行最高3.1Mbps，上行最高1.8Mbps
- **延迟**: 100-500ms
- **覆盖**: 广泛，适合偏远地区

**4G LTE技术特点：**
- **LTE Cat4**: 下行最高150Mbps，上行最高50Mbps
- **LTE Cat6**: 下行最高300Mbps，上行最高50Mbps
- **延迟**: 20-50ms
- **覆盖**: 城市和主要交通干线

#### 1.3.2 调制解调器连接方式

**USB调制解调器示例：**
```bash
# 华为E3372 4G USB调制解调器
设备识别：
Bus 001 Device 003: ID 12d1:14db Huawei Technologies Co., Ltd. E353/E3131

# 中兴MF823 4G USB调制解调器  
设备识别：
Bus 001 Device 004: ID 19d2:1405 ZTE WCDMA Technologies MSM

# 移远EC25 4G模块（通过USB转串口）
设备识别：
Bus 001 Device 005: ID 2c7c:0125 Quectel Wireless Solutions Co., Ltd. EC25 LTE modem
```

**PCIe/mPCIe模块示例：**
```bash
# Sierra Wireless EM7455 4G模块
lspci输出：
01:00.0 Network controller: Sierra Wireless, Inc. EM7455

# 移远EC25-E mPCIe模块
lspci输出：
02:00.0 Network controller: Qualcomm Technologies, Inc. EC25-E LTE Module
```

### 1.4 WAN备份工作原理

#### 1.4.1 连接状态监控

**监控机制：**
```bash
# 1. 网络连通性检测
ping -c 3 *******          # DNS服务器连通性
ping -c 3 ***************   # 备用DNS服务器
curl -s --max-time 5 http://www.baidu.com  # HTTP连通性测试

# 2. 接口状态检测
ip link show eth0.2         # 物理接口状态
ifstatus wan                # netifd接口状态

# 3. 路由表检测
ip route show default       # 默认路由存在性
```

**监控脚本示例：**
```bash
#!/bin/sh
# /usr/bin/wan-monitor.sh

WAN_INTERFACE="wan"
BACKUP_INTERFACE="wan_4g"
TEST_HOSTS="******* ***************"
FAIL_COUNT_FILE="/tmp/wan_fail_count"
MAX_FAIL_COUNT=3

check_wan_connectivity() {
    local fail_count=0
    
    for host in $TEST_HOSTS; do
        if ! ping -c 2 -W 5 -I $WAN_INTERFACE $host >/dev/null 2>&1; then
            fail_count=$((fail_count + 1))
        fi
    done
    
    return $fail_count
}

handle_wan_failure() {
    local current_fail_count=$(cat $FAIL_COUNT_FILE 2>/dev/null || echo 0)
    current_fail_count=$((current_fail_count + 1))
    echo $current_fail_count > $FAIL_COUNT_FILE
    
    if [ $current_fail_count -ge $MAX_FAIL_COUNT ]; then
        logger "WAN connection failed $current_fail_count times, switching to 4G backup"
        switch_to_backup
        echo 0 > $FAIL_COUNT_FILE
    fi
}

switch_to_backup() {
    # 禁用主WAN接口
    ifdown $WAN_INTERFACE
    
    # 启用4G备份接口
    ifup $BACKUP_INTERFACE
    
    # 更新路由表
    ip route del default
    ip route add default dev $BACKUP_INTERFACE
    
    # 发送通知
    logger "Switched to 4G backup connection"
    send_notification "WAN backup activated"
}

# 主监控循环
while true; do
    if check_wan_connectivity; then
        # WAN连接正常，重置失败计数
        echo 0 > $FAIL_COUNT_FILE
        
        # 如果当前使用备份连接，检查是否可以切回主连接
        if [ "$(cat /tmp/current_wan)" = "backup" ]; then
            check_primary_recovery
        fi
    else
        # WAN连接失败
        handle_wan_failure
    fi
    
    sleep 30
done
```

#### 1.4.2 自动切换机制

**切换逻辑流程：**
```bash
# 故障检测和切换流程
1. 主WAN连接监控
   ├── 连通性测试 (ping/http)
   ├── 接口状态检查
   └── 连续失败计数

2. 故障判定
   ├── 连续失败次数 >= 阈值
   ├── 接口物理断开
   └── 路由表异常

3. 切换到备份连接
   ├── 禁用主WAN接口
   ├── 启用4G备份接口
   ├── 更新路由表
   ├── 更新DNS配置
   └── 发送故障通知

4. 主连接恢复检测
   ├── 定期测试主连接
   ├── 连续成功次数 >= 阈值
   └── 切回主连接

5. 切回主连接
   ├── 启用主WAN接口
   ├── 禁用4G备份接口
   ├── 恢复路由表
   └── 发送恢复通知
```

#### 1.4.3 流量管理示例

**4G流量控制：**
```bash
# 流量监控和限制
# 1. 实时流量统计
iptables -I FORWARD -o ppp0 -j ACCEPT
iptables -I FORWARD -i ppp0 -j ACCEPT
iptables -L FORWARD -v -n | grep ppp0

# 2. 月流量限制
MONTHLY_LIMIT="10GB"  # 10GB月流量限制
CURRENT_USAGE=$(cat /proc/net/dev | grep ppp0 | awk '{print $2+$10}')

if [ $CURRENT_USAGE -gt $MONTHLY_LIMIT ]; then
    logger "4G monthly limit exceeded, disabling backup"
    ifdown wan_4g
fi

# 3. 应用优先级控制
# 高优先级：VoIP、视频会议
iptables -t mangle -A FORWARD -p udp --dport 5060 -j MARK --set-mark 1
# 中优先级：Web浏览、邮件
iptables -t mangle -A FORWARD -p tcp --dport 80 -j MARK --set-mark 2
# 低优先级：文件下载、更新
iptables -t mangle -A FORWARD -p tcp --dport 443 -j MARK --set-mark 3

# 4. 带宽限制
tc qdisc add dev ppp0 root handle 1: htb default 30
tc class add dev ppp0 parent 1: classid 1:1 htb rate 10mbit
tc class add dev ppp0 parent 1:1 classid 1:10 htb rate 5mbit ceil 8mbit  # 高优先级
tc class add dev ppp0 parent 1:1 classid 1:20 htb rate 3mbit ceil 5mbit  # 中优先级
tc class add dev ppp0 parent 1:1 classid 1:30 htb rate 2mbit ceil 3mbit  # 低优先级
```

## 2. OpenWrt 21.02中的3G/4G支持包

### 2.1 核心软件包调研

#### 2.1.1 调制解调器管理包
```bash
# ModemManager - 现代调制解调器管理
opkg list | grep modemmanager
# modemmanager - 1.16.6-1 - ModemManager is a DBus-activated daemon
# libmm-glib0 - 1.16.6-1 - GLib binding for ModemManager
# modemmanager-rpcd - 1.16.6-1 - OpenWrt ubus integration for ModemManager

# 安装ModemManager完整套件
opkg install modemmanager libmm-glib0 modemmanager-rpcd
# Installing modemmanager (1.16.6-1) to root...
# Installing libmm-glib0 (1.16.6-1) to root...
# Installing modemmanager-rpcd (1.16.6-1) to root...
# Installing libqmi (1.28.6-1) to root...
# Installing libmbim (1.24.8-1) to root...
```

#### 2.1.2 传统拨号支持包
```bash
# PPP拨号支持
opkg list | grep ppp
# ppp - 2.4.9.git-2021-01-04-3 - PPP daemon
# ppp-mod-pppoe - 2.4.9.git-2021-01-04-3 - PPPoE plugin for ppp
# ppp-mod-pppoa - 2.4.9.git-2021-01-04-3 - PPPoA plugin for ppp
# ppp-mod-radius - 2.4.9.git-2021-01-04-3 - RADIUS plugin for ppp
# ppp-mod-chat - 2.4.9.git-2021-01-04-3 - Chat script support for ppp

# 3G/4G特定支持
# comgt - 0.32-33 - Option/Vodafone 3G/GPRS control tool
# comgt-ncm - 0.32-33 - NCM 3G/4G Support for comgt
# usb-modeswitch - 2.6.1-1 - USB mode switching utility

# 安装3G/4G拨号支持
opkg install ppp comgt comgt-ncm usb-modeswitch
```

#### 2.1.3 USB调制解调器驱动
```bash
# USB串口驱动
opkg list | grep usb-serial
# kmod-usb-serial - 5.4.155-1 - USB Serial device support
# kmod-usb-serial-option - 5.4.155-1 - USB Serial driver for GSM modems
# kmod-usb-serial-wwan - 5.4.155-1 - USB Serial driver for 3G/4G modems

# USB网络驱动
# kmod-usb-net - 5.4.155-1 - USB Network device support
# kmod-usb-net-cdc-ether - 5.4.155-1 - USB CDC Ethernet support
# kmod-usb-net-cdc-ncm - 5.4.155-1 - USB CDC NCM support
# kmod-usb-net-cdc-mbim - 5.4.155-1 - USB CDC MBIM support
# kmod-usb-net-qmi-wwan - 5.4.155-1 - USB QMI WWAN support

# 安装USB调制解调器驱动
opkg install kmod-usb-serial kmod-usb-serial-option kmod-usb-serial-wwan \
             kmod-usb-net kmod-usb-net-cdc-ether kmod-usb-net-cdc-ncm \
             kmod-usb-net-cdc-mbim kmod-usb-net-qmi-wwan
```

#### 2.1.4 QMI/MBIM协议支持
```bash
# QMI协议支持 (Qualcomm MSM Interface)
opkg list | grep qmi
# libqmi - 1.28.6-1 - QMI library and utilities
# uqmi - 2021-05-27-a84c0bb6-1 - Tiny QMI command line utility

# MBIM协议支持 (Mobile Broadband Interface Model)
opkg list | grep mbim
# libmbim - 1.24.8-1 - MBIM library and utilities
# umbim - 2021-04-05-24d5adb5-1 - Tiny MBIM command line utility

# 安装QMI/MBIM支持
opkg install libqmi uqmi libmbim umbim
```

### 2.2 支持的调制解调器设备

#### 2.2.1 华为设备支持
```bash
# 华为E3372 4G USB调制解调器
# USB ID: 12d1:14db
# 协议: NCM/RNDIS
# 频段: LTE B1/B3/B7/B8/B20/B38/B40/B41

# 设备识别和配置
echo '12d1 14db' > /sys/bus/usb-serial/drivers/option/new_id

# UCI网络配置
config interface 'wan_4g'
    option ifname 'eth1'
    option proto 'dhcp'
    option metric '10'

# 华为E3531 3G USB调制解调器
# USB ID: 12d1:15ca
# 协议: PPP/串口拨号
# 频段: UMTS B1/B8

config interface 'wan_3g'
    option ifname '/dev/ttyUSB0'
    option proto '3g'
    option device '/dev/ttyUSB0'
    option apn 'internet'
    option username ''
    option password ''
    option service 'umts'
```

#### 2.2.2 中兴设备支持
```bash
# 中兴MF823 4G USB调制解调器
# USB ID: 19d2:1405
# 协议: RNDIS/NCM
# 频段: LTE B1/B3/B7/B8/B20

# 设备配置
config interface 'wan_4g_zte'
    option ifname 'usb0'
    option proto 'dhcp'
    option metric '10'

# 中兴MF190 3G USB调制解调器
# USB ID: 19d2:0031
# 协议: PPP/串口拨号

config interface 'wan_3g_zte'
    option ifname '/dev/ttyUSB2'
    option proto '3g'
    option device '/dev/ttyUSB2'
    option apn 'cmnet'
    option service 'umts'
```

#### 2.2.3 移远模块支持
```bash
# 移远EC25 4G模块
# USB ID: 2c7c:0125
# 协议: QMI/MBIM/PPP
# 频段: LTE Cat4全频段

# QMI配置方式
config interface 'wan_4g_quectel'
    option proto 'qmi'
    option device '/dev/cdc-wdm0'
    option apn 'internet'
    option auth 'none'
    option pdptype 'ipv4'
    option metric '10'

# MBIM配置方式
config interface 'wan_4g_mbim'
    option proto 'mbim'
    option device '/dev/cdc-wdm0'
    option apn 'internet'
    option auth 'none'
    option pdptype 'ipv4'
    option metric '10'
```

#### 2.2.4 Sierra Wireless设备支持
```bash
# Sierra Wireless EM7455 4G模块
# PCIe接口: 1199:9071
# 协议: QMI/MBIM
# 频段: LTE Cat6全频段

# QMI配置
config interface 'wan_4g_sierra'
    option proto 'qmi'
    option device '/dev/cdc-wdm0'
    option apn 'internet'
    option auth 'none'
    option pdptype 'ipv4v6'
    option metric '10'
    option delegate '0'
```

### 2.3 运营商APN配置

#### 2.3.1 中国移动配置
```bash
# 中国移动4G配置
config interface 'wan_4g_cmcc'
    option proto 'qmi'
    option device '/dev/cdc-wdm0'
    option apn 'cmnet'          # 公网APN
    option auth 'none'
    option pdptype 'ipv4'
    option metric '10'

# 中国移动专网APN
config interface 'wan_4g_cmcc_vpn'
    option proto 'qmi'
    option device '/dev/cdc-wdm0'
    option apn 'cmiot'          # 物联网专网APN
    option username 'cmiot'
    option password 'cmiot'
    option auth 'pap'
    option pdptype 'ipv4'
```

#### 2.3.2 中国联通配置
```bash
# 中国联通4G配置
config interface 'wan_4g_cucc'
    option proto 'qmi'
    option device '/dev/cdc-wdm0'
    option apn '3gnet'          # 公网APN
    option auth 'none'
    option pdptype 'ipv4'
    option metric '10'

# 中国联通物联网APN
config interface 'wan_4g_cucc_iot'
    option proto 'qmi'
    option device '/dev/cdc-wdm0'
    option apn 'unim2m.njm2mapn'  # 物联网APN
    option username 'uninet'
    option password 'uninet'
    option auth 'chap'
```

#### 2.3.3 中国电信配置
```bash
# 中国电信4G配置
config interface 'wan_4g_ctcc'
    option proto 'qmi'
    option device '/dev/cdc-wdm0'
    option apn 'ctnet'          # 公网APN
    option auth 'none'
    option pdptype 'ipv4'
    option metric '10'

# 中国电信物联网APN
config interface 'wan_4g_ctcc_iot'
    option proto 'qmi'
    option device '/dev/cdc-wdm0'
    option apn 'ctiot'          # 物联网APN
    option username '<EMAIL>'
    option password 'vnet.mobi'
    option auth 'pap'
```

### 2.4 WAN备份配置实现

#### 2.4.1 多WAN接口配置
```bash
# /etc/config/network - 多WAN配置
# 主WAN接口 (光纤/ADSL)
config interface 'wan'
    option ifname 'eth0.2'
    option proto 'pppoe'
    option username 'broadband_user'
    option password 'broadband_pass'
    option metric '1'           # 最高优先级
    option auto '1'

# 4G备份接口
config interface 'wan_4g'
    option proto 'qmi'
    option device '/dev/cdc-wdm0'
    option apn 'cmnet'
    option metric '10'          # 较低优先级
    option auto '0'             # 手动启用

# 接口依赖关系
config interface 'wan_backup'
    option proto 'none'
    option ifname '@wan_4g'
    option depends 'wan'        # 依赖主WAN状态
```

#### 2.4.2 mwan3多WAN管理
```bash
# 安装mwan3多WAN管理包
opkg install mwan3 luci-app-mwan3

# /etc/config/mwan3 - 多WAN配置
config globals 'globals'
    option mmx_mask '0x3F00'
    option local_source 'lan'

# 定义WAN接口
config interface 'wan'
    option enabled '1'
    option initial_state 'online'
    option family 'ipv4'
    option track_ip '*******'
    option track_ip '***************'
    option track_method 'ping'
    option reliability '2'
    option count '1'
    option timeout '2'
    option interval '5'
    option down '3'
    option up '8'

config interface 'wan_4g'
    option enabled '1'
    option initial_state 'offline'
    option family 'ipv4'
    option track_ip '*******'
    option track_method 'ping'
    option reliability '1'
    option count '1'
    option timeout '2'
    option interval '10'
    option down '3'
    option up '8'

# 定义成员
config member 'wan_m1_w3'
    option interface 'wan'
    option metric '1'
    option weight '3'

config member 'wan_4g_m2_w1'
    option interface 'wan_4g'
    option metric '2'
    option weight '1'

# 定义策略
config policy 'wan_only'
    list use_member 'wan_m1_w3'

config policy 'backup_policy'
    list use_member 'wan_m1_w3'
    list use_member 'wan_4g_m2_w1'

# 定义规则
config rule 'default_rule'
    option dest_ip '0.0.0.0/0'
    option use_policy 'backup_policy'
    option proto 'all'
```

## 3. LuCI Web界面实现

### 3.1 现有LuCI支持状态

#### 3.1.1 LuCI多WAN管理界面
```bash
# 检查LuCI多WAN支持
opkg list | grep luci-app-mwan3
# luci-app-mwan3 - git-21.295.67054-c5b6c35 - LuCI support for the mwan3 multiwan hotplug script

# 安装LuCI多WAN界面
opkg install luci-app-mwan3
# Installing luci-app-mwan3 (git-21.295.67054-c5b6c35) to root...

# 访问路径：Network -> Multi-WAN Manager
```

#### 3.1.2 LuCI 3G/4G调制解调器支持
```bash
# ModemManager LuCI支持
opkg list | grep luci-proto
# luci-proto-3g - git-21.295.67054-c5b6c35 - Support for 3G
# luci-proto-qmi - git-21.295.67054-c5b6c35 - Support for QMI
# luci-proto-mbim - git-21.295.67054-c5b6c35 - Support for MBIM
# luci-proto-ncm - git-21.295.67054-c5b6c35 - Support for NCM

# 安装协议支持
opkg install luci-proto-3g luci-proto-qmi luci-proto-mbim luci-proto-ncm
```

### 3.2 增强的WAN备份管理界面

#### 3.2.1 WAN备份状态监控页面
```lua
-- /usr/lib/lua/luci/model/cbi/wan_backup_status.lua
local m, s, o
local sys = require "luci.sys"
local util = require "luci.util"
local uci = require "luci.model.uci".cursor()

m = SimpleForm("wan_backup_status", translate("WAN Backup Status"))
m.reset = false
m.submit = false

-- 主WAN状态
s = m:section(SimpleSection, translate("Primary WAN Status"))

local wan_status = {}
wan_status.interface = "wan"
wan_status.up = sys.call("ifstatus wan | grep '\"up\": true' > /dev/null") == 0
wan_status.ip = util.trim(sys.exec("ifstatus wan | jsonfilter -e '@.ipv4-address[0].address'"))
wan_status.gateway = util.trim(sys.exec("ifstatus wan | jsonfilter -e '@.route[0].nexthop'"))

o = s:option(DummyValue, "wan_status", translate("Status"))
o.value = wan_status.up and translate("Online") or translate("Offline")

o = s:option(DummyValue, "wan_ip", translate("IP Address"))
o.value = wan_status.ip or "N/A"

o = s:option(DummyValue, "wan_gateway", translate("Gateway"))
o.value = wan_status.gateway or "N/A"

-- 4G备份状态
s = m:section(SimpleSection, translate("4G Backup Status"))

local backup_status = {}
backup_status.interface = "wan_4g"
backup_status.up = sys.call("ifstatus wan_4g | grep '\"up\": true' > /dev/null") == 0
backup_status.ip = util.trim(sys.exec("ifstatus wan_4g | jsonfilter -e '@.ipv4-address[0].address'"))
backup_status.signal = util.trim(sys.exec("uqmi -d /dev/cdc-wdm0 --get-signal-info | jsonfilter -e '@.rssi'"))

o = s:option(DummyValue, "backup_status", translate("Status"))
o.value = backup_status.up and translate("Active") or translate("Standby")

o = s:option(DummyValue, "backup_ip", translate("IP Address"))
o.value = backup_status.ip or "N/A"

o = s:option(DummyValue, "signal_strength", translate("Signal Strength"))
o.value = backup_status.signal and (backup_status.signal .. " dBm") or "N/A"

-- 流量统计
s = m:section(SimpleSection, translate("Traffic Statistics"))

local traffic_stats = {}
traffic_stats.wan_rx = util.trim(sys.exec("cat /sys/class/net/eth0.2/statistics/rx_bytes"))
traffic_stats.wan_tx = util.trim(sys.exec("cat /sys/class/net/eth0.2/statistics/tx_bytes"))
traffic_stats.backup_rx = util.trim(sys.exec("cat /sys/class/net/wwan0/statistics/rx_bytes 2>/dev/null || echo 0"))
traffic_stats.backup_tx = util.trim(sys.exec("cat /sys/class/net/wwan0/statistics/tx_bytes 2>/dev/null || echo 0"))

-- 转换字节为可读格式
local function format_bytes(bytes)
    local units = {"B", "KB", "MB", "GB"}
    local size = tonumber(bytes) or 0
    local unit_index = 1

    while size >= 1024 and unit_index < #units do
        size = size / 1024
        unit_index = unit_index + 1
    end

    return string.format("%.2f %s", size, units[unit_index])
end

o = s:option(DummyValue, "wan_traffic", translate("Primary WAN Traffic"))
o.value = string.format("↓ %s / ↑ %s",
    format_bytes(traffic_stats.wan_rx),
    format_bytes(traffic_stats.wan_tx))

o = s:option(DummyValue, "backup_traffic", translate("4G Backup Traffic"))
o.value = string.format("↓ %s / ↑ %s",
    format_bytes(traffic_stats.backup_rx),
    format_bytes(traffic_stats.backup_tx))

return m
```

#### 3.2.2 WAN备份配置页面
```lua
-- /usr/lib/lua/luci/model/cbi/wan_backup_config.lua
local m, s, o
local uci = require "luci.model.uci".cursor()

m = Map("wan_backup", translate("WAN Backup Configuration"),
    translate("Configure automatic failover from primary WAN to 4G backup"))

-- 基本设置
s = m:section(TypedSection, "global", translate("Global Settings"))
s.anonymous = true
s.addremove = false

o = s:option(Flag, "enabled", translate("Enable WAN Backup"))
o.default = "0"

o = s:option(Value, "check_interval", translate("Check Interval"))
o.datatype = "uinteger"
o.default = "30"
o.description = translate("Seconds between connectivity checks")

o = s:option(Value, "fail_threshold", translate("Failure Threshold"))
o.datatype = "uinteger"
o.default = "3"
o.description = translate("Consecutive failures before switching to backup")

o = s:option(Value, "recovery_threshold", translate("Recovery Threshold"))
o.datatype = "uinteger"
o.default = "5"
o.description = translate("Consecutive successes before switching back to primary")

-- 监控设置
s = m:section(TypedSection, "monitor", translate("Connectivity Monitoring"))
s.anonymous = true
s.addremove = false

o = s:option(DynamicList, "test_hosts", translate("Test Hosts"))
o.default = {"*******", "***************"}
o.description = translate("Hosts to ping for connectivity testing")

o = s:option(Value, "ping_timeout", translate("Ping Timeout"))
o.datatype = "uinteger"
o.default = "5"
o.description = translate("Ping timeout in seconds")

o = s:option(Value, "ping_count", translate("Ping Count"))
o.datatype = "uinteger"
o.default = "2"
o.description = translate("Number of ping packets per test")

-- 4G设置
s = m:section(TypedSection, "backup", translate("4G Backup Settings"))
s.anonymous = true
s.addremove = false

o = s:option(ListValue, "device_type", translate("Device Type"))
o:value("qmi", "QMI")
o:value("mbim", "MBIM")
o:value("3g", "3G/PPP")
o:value("ncm", "NCM")
o.default = "qmi"

o = s:option(Value, "device_path", translate("Device Path"))
o.default = "/dev/cdc-wdm0"
o.description = translate("Path to the modem device")

o = s:option(Value, "apn", translate("APN"))
o.default = "internet"
o.description = translate("Access Point Name from your carrier")

o = s:option(ListValue, "carrier", translate("Carrier"))
o:value("cmcc", translate("China Mobile"))
o:value("cucc", translate("China Unicom"))
o:value("ctcc", translate("China Telecom"))
o:value("custom", translate("Custom"))

-- 流量控制
s = m:section(TypedSection, "traffic", translate("Traffic Control"))
s.anonymous = true
s.addremove = false

o = s:option(Flag, "enable_limit", translate("Enable Traffic Limit"))
o.default = "1"

o = s:option(Value, "monthly_limit", translate("Monthly Limit (MB)"))
o.datatype = "uinteger"
o.default = "10240"
o.description = translate("Monthly traffic limit in megabytes")

o = s:option(Value, "daily_limit", translate("Daily Limit (MB)"))
o.datatype = "uinteger"
o.default = "1024"
o.description = translate("Daily traffic limit in megabytes")

-- 通知设置
s = m:section(TypedSection, "notification", translate("Notification Settings"))
s.anonymous = true
s.addremove = false

o = s:option(Flag, "enable_email", translate("Enable Email Notifications"))
o.default = "0"

o = s:option(Value, "email_server", translate("SMTP Server"))
o.depends("enable_email", "1")

o = s:option(Value, "email_user", translate("Email Username"))
o.depends("enable_email", "1")

o = s:option(Value, "email_password", translate("Email Password"))
o.password = true
o.depends("enable_email", "1")

o = s:option(Value, "email_to", translate("Recipient Email"))
o.depends("enable_email", "1")

return m
```

### 3.3 实时监控仪表板

#### 3.3.1 JavaScript实时状态更新
```javascript
// /www/luci-static/resources/view/wan_backup/dashboard.js
'use strict';
'require view';
'require dom';
'require request';
'require poll';

return view.extend({
    title: _('WAN Backup Dashboard'),

    load: function() {
        return Promise.all([
            this.loadWanStatus(),
            this.loadBackupStatus(),
            this.loadTrafficStats()
        ]);
    },

    loadWanStatus: function() {
        return request.get('/cgi-bin/luci/admin/network/wan_backup/status', {
            timeout: 5000
        }).then(function(response) {
            return response.json();
        });
    },

    loadBackupStatus: function() {
        return request.get('/cgi-bin/luci/admin/network/wan_backup/backup_status', {
            timeout: 5000
        }).then(function(response) {
            return response.json();
        });
    },

    loadTrafficStats: function() {
        return request.get('/cgi-bin/luci/admin/network/wan_backup/traffic', {
            timeout: 5000
        }).then(function(response) {
            return response.json();
        });
    },

    render: function(data) {
        var wanStatus = data[0];
        var backupStatus = data[1];
        var trafficStats = data[2];

        var view = E('div', { 'class': 'cbi-section' }, [
            E('h2', _('WAN Backup Dashboard')),

            // 状态概览
            E('div', { 'class': 'cbi-section-node' }, [
                E('h3', _('Connection Status')),
                this.renderConnectionStatus(wanStatus, backupStatus)
            ]),

            // 信号强度
            E('div', { 'class': 'cbi-section-node' }, [
                E('h3', _('4G Signal Strength')),
                this.renderSignalStrength(backupStatus.signal)
            ]),

            // 流量统计
            E('div', { 'class': 'cbi-section-node' }, [
                E('h3', _('Traffic Statistics')),
                this.renderTrafficStats(trafficStats)
            ]),

            // 事件日志
            E('div', { 'class': 'cbi-section-node' }, [
                E('h3', _('Recent Events')),
                this.renderEventLog(data.events)
            ])
        ]);

        // 启动定时更新
        poll.add(this.updateStatus.bind(this), 5);

        return view;
    },

    renderConnectionStatus: function(wan, backup) {
        var primaryClass = wan.up ? 'status-online' : 'status-offline';
        var backupClass = backup.active ? 'status-active' : 'status-standby';

        return E('div', { 'class': 'connection-status' }, [
            E('div', { 'class': 'connection-item' }, [
                E('div', { 'class': 'connection-icon ' + primaryClass }, '🌐'),
                E('div', { 'class': 'connection-info' }, [
                    E('h4', _('Primary WAN')),
                    E('p', wan.up ? _('Online') : _('Offline')),
                    E('p', wan.ip || 'N/A')
                ])
            ]),
            E('div', { 'class': 'connection-arrow' }, '→'),
            E('div', { 'class': 'connection-item' }, [
                E('div', { 'class': 'connection-icon ' + backupClass }, '📱'),
                E('div', { 'class': 'connection-info' }, [
                    E('h4', _('4G Backup')),
                    E('p', backup.active ? _('Active') : _('Standby')),
                    E('p', backup.ip || 'N/A')
                ])
            ])
        ]);
    },

    renderSignalStrength: function(signal) {
        var strength = parseInt(signal) || -100;
        var bars = Math.max(0, Math.min(4, Math.floor((strength + 100) / 25)));
        var strengthText = ['Poor', 'Fair', 'Good', 'Excellent'][bars] || 'No Signal';

        return E('div', { 'class': 'signal-strength' }, [
            E('div', { 'class': 'signal-bars' },
                Array.from({length: 4}, function(_, i) {
                    return E('div', {
                        'class': 'signal-bar ' + (i < bars ? 'active' : 'inactive')
                    });
                })
            ),
            E('div', { 'class': 'signal-info' }, [
                E('p', strength + ' dBm'),
                E('p', _(strengthText))
            ])
        ]);
    },

    renderTrafficStats: function(stats) {
        return E('table', { 'class': 'table' }, [
            E('thead', [
                E('tr', [
                    E('th', _('Interface')),
                    E('th', _('Download')),
                    E('th', _('Upload')),
                    E('th', _('Total'))
                ])
            ]),
            E('tbody', [
                E('tr', [
                    E('td', _('Primary WAN')),
                    E('td', this.formatBytes(stats.wan.rx)),
                    E('td', this.formatBytes(stats.wan.tx)),
                    E('td', this.formatBytes(stats.wan.rx + stats.wan.tx))
                ]),
                E('tr', [
                    E('td', _('4G Backup')),
                    E('td', this.formatBytes(stats.backup.rx)),
                    E('td', this.formatBytes(stats.backup.tx)),
                    E('td', this.formatBytes(stats.backup.rx + stats.backup.tx))
                ])
            ])
        ]);
    },

    formatBytes: function(bytes) {
        var units = ['B', 'KB', 'MB', 'GB'];
        var size = parseInt(bytes) || 0;
        var unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return size.toFixed(2) + ' ' + units[unitIndex];
    },

    updateStatus: function() {
        return this.load().then(function(data) {
            // 更新页面元素
            this.updateConnectionStatus(data[0], data[1]);
            this.updateSignalStrength(data[1].signal);
            this.updateTrafficStats(data[2]);
        }.bind(this));
    },

    handleSaveApply: null,
    handleSave: null,
    handleReset: null
});
```

## 4. OpenWrt 21.02上的3G/4G备份工作量评估

### 4.1 现有基础分析

| 组件 | OpenWrt 21.02状态 | 可用性 | 需要工作 |
|------|------------------|--------|----------|
| ModemManager | ✅ 1.16.6-1 | 生产就绪 | 配置优化 |
| QMI/MBIM支持 | ✅ libqmi 1.28.6 | 完整支持 | 设备适配 |
| PPP拨号 | ✅ ppp 2.4.9 | 成熟稳定 | 脚本优化 |
| USB驱动 | ✅ 内核5.4.55 | 完整支持 | 设备识别 |
| mwan3多WAN | ✅ 可选安装 | 基础功能 | 备份逻辑 |
| LuCI界面 | ✅ 基础支持 | 基础功能 | 界面增强 |

### 4.2 详细工作量估算

| 开发任务 | 工作内容 | 工作量 | 技术难度 |
|----------|----------|--------|----------|
| 设备驱动适配 | 新设备USB ID添加、驱动测试 | 3-5人日 | 简单 |
| 自动切换逻辑 | 连接监控、故障检测、切换脚本 | 8-12人日 | 中等 |
| 流量管理 | 流量统计、限制、告警机制 | 5-8人日 | 中等 |
| LuCI界面开发 | 状态监控、配置管理、仪表板 | 10-15人日 | 中等 |
| 运营商配置 | APN数据库、自动识别配置 | 4-6人日 | 简单 |
| 信号监控 | 信号强度、网络质量监控 | 3-5人日 | 简单 |
| 通知系统 | 邮件、短信、日志通知 | 4-6人日 | 简单 |
| 性能优化 | 切换速度、资源占用优化 | 5-8人日 | 中等 |
| 安全增强 | VPN集成、访问控制 | 6-10人日 | 中等 |
| 测试验证 | 多设备测试、稳定性测试 | 8-12人日 | 中等 |
| 文档编写 | 用户手册、配置指南 | 3-5人日 | 简单 |
| **总计** | | **59-92人日** | |

### 4.3 工作量分解 (按模块)

#### 4.3.1 核心备份功能 (16-25人日)
- 连接状态监控脚本
- 自动切换逻辑实现
- 故障恢复机制
- 流量统计和限制

#### 4.3.2 设备支持和驱动 (7-11人日)
- 新调制解调器设备适配
- USB设备自动识别
- 运营商APN数据库
- 设备配置模板

#### 4.3.3 用户界面开发 (13-20人日)
- LuCI状态监控页面
- 配置管理界面
- 实时仪表板
- 移动端适配

#### 4.3.4 监控和通知 (7-11人日)
- 信号强度监控
- 网络质量检测
- 邮件/短信通知
- 事件日志系统

#### 4.3.5 安全和性能 (11-18人日)
- VPN自动重连
- 访问控制策略
- 性能优化调整
- 安全配置增强

#### 4.3.6 测试和文档 (11-17人日)
- 多设备兼容性测试
- 长期稳定性测试
- 用户文档编写
- 故障排除指南

### 4.4 技术风险评估

| 风险项 | 风险等级 | 影响 | 缓解措施 |
|--------|----------|------|----------|
| 设备兼容性 | 中 | 部分设备无法识别 | 扩展设备数据库 |
| 切换延迟 | 中 | 业务中断时间过长 | 优化检测算法 |
| 流量成本 | 高 | 4G流量费用过高 | 严格流量控制 |
| 信号不稳定 | 中 | 频繁切换影响体验 | 智能切换策略 |
| 运营商限制 | 中 | APN配置复杂 | 预配置模板 |

### 4.5 OpenWrt 21.02特定优势

**平台优势：**
- **ModemManager成熟**: 1.16.6版本支持主流设备
- **内核驱动完整**: Linux 5.4.55包含最新USB调制解调器驱动
- **协议支持全面**: QMI/MBIM/NCM/PPP全协议支持
- **mwan3可用**: 多WAN管理基础设施完善

**实施建议：**
- **总工作量**: 59-92人日 (3.0-4.6人月)
- **团队配置**: 3-4人开发团队
- **完成时间**: 8-12周
- **技术风险**: 中等

### 4.6 成本效益分析

#### 4.6.1 开发成本
```bash
# 人力成本估算 (按中级工程师计算)
开发人员: 3人 × 3个月 = 9人月
测试人员: 1人 × 1个月 = 1人月
项目管理: 0.5人 × 4个月 = 2人月
总人力成本: 12人月

# 硬件测试成本
调制解调器设备: 20款 × 500元 = 10,000元
SIM卡和流量费: 3运营商 × 12个月 × 200元 = 7,200元
测试设备: 路由器、交换机等 = 15,000元
总硬件成本: 32,200元
```

#### 4.6.2 商业价值
```bash
# 目标市场
企业分支机构: 银行、零售、制造业
家庭办公: 远程工作、在线教育
工业物联网: 设备监控、数据采集
应急通信: 灾难恢复、临时网络

# 预期收益
- 提高网络可靠性: 99.9%可用性保障
- 降低业务中断损失: 每小时中断成本节省
- 扩大产品竞争力: 差异化功能优势
- 增加客户粘性: 完整解决方案提供
```

## 5. 实施路线图

### 5.1 第一阶段 (3周) - 核心功能开发
- **Week 1**: 连接监控和故障检测逻辑
- **Week 2**: 自动切换机制实现
- **Week 3**: 基础流量管理功能

**交付物:**
- WAN备份核心脚本
- 基础配置文件模板
- 简单的切换测试

### 5.2 第二阶段 (3周) - 设备支持扩展
- **Week 4**: 主流调制解调器设备适配
- **Week 5**: 运营商APN数据库建设
- **Week 6**: 设备自动识别和配置

**交付物:**
- 支持20+主流设备
- 三大运营商APN配置
- 设备即插即用功能

### 5.3 第三阶段 (3周) - 用户界面开发
- **Week 7**: LuCI状态监控页面
- **Week 8**: 配置管理界面
- **Week 9**: 实时仪表板和移动适配

**交付物:**
- 完整的Web管理界面
- 实时状态监控
- 移动端友好界面

### 5.4 第四阶段 (3周) - 功能完善和测试
- **Week 10**: 通知系统和日志功能
- **Week 11**: 性能优化和安全增强
- **Week 12**: 全面测试和文档编写

**交付物:**
- 完整的通知系统
- 性能优化版本
- 用户文档和故障排除指南

## 6. 关键技术实现要点

### 6.1 智能切换策略
```bash
# 多层次故障检测
1. 物理层检测: 网线连接、接口状态
2. 网络层检测: IP连通性、路由可达性
3. 应用层检测: DNS解析、HTTP访问
4. 业务层检测: 关键服务可用性

# 切换决策算法
- 加权评分机制
- 历史故障统计
- 时间窗口分析
- 成本效益考量
```

### 6.2 流量优化策略
```bash
# 智能流量管理
1. 应用识别和分类
2. 优先级队列调度
3. 带宽自适应分配
4. 压缩和缓存优化

# 成本控制机制
- 实时流量监控
- 预算告警系统
- 自动限速保护
- 业务降级策略
```

### 6.3 可靠性保障
```bash
# 高可用性设计
1. 冗余检测机制
2. 快速故障恢复
3. 状态持久化
4. 看门狗保护

# 容错处理
- 异常情况处理
- 自动重试机制
- 降级服务模式
- 人工干预接口
```

## 7. 结论

基于OpenWrt 21.02的3G/4G备份功能具有良好的技术基础，ModemManager和相关协议栈提供了完整的移动网络支持。主要工作集中在智能切换逻辑、用户界面开发和设备适配优化，预计3.0-4.6人月可以完成完整的WAN备份解决方案。

**关键成功因素:**
- 智能的故障检测和切换策略
- 广泛的设备兼容性支持
- 直观的用户管理界面
- 有效的流量成本控制
- 可靠的长期稳定性

该功能将显著提高网络可靠性，为企业和家庭用户提供不间断的网络连接保障。
