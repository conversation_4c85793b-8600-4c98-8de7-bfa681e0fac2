# EasyMesh 工作机制深度分析

## 1. EasyMesh 概述

### 1.1 技术定义
**Wi-Fi EasyMesh** 是 Wi-Fi 联盟制定的多接入点 (Multi-AP) 网络标准，基于 IEEE 1905.1 协议实现。它定义了一套标准化的协议和机制，用于创建、管理和优化家庭和企业环境中的无线网状网络。

### 1.2 核心架构

#### 1.2.1 Multi-AP 的作用和意义

**Multi-AP 是什么？**
Multi-AP (多接入点) 是 Wi-Fi 联盟制定的技术规范，用于解决传统单一 AP 无法覆盖大面积区域的问题。

```
传统单 AP vs Multi-AP 对比:

传统单 AP 部署:                    Multi-AP (EasyMesh) 部署:
┌─────────────────┐                ┌─────────────────┐
│   Single AP     │                │   Controller    │ (协调管理)
│                 │                │   (Main AP)     │
│ 问题:            │                └─────────┬───────┘
│ • 覆盖范围有限   │                          │
│ • 信号死角      │                ┌─────────────────┐
│ • 容量瓶颈      │                │     Agent       │ (扩展覆盖)
│ • 无法协调      │                │   (Satellite)   │
└─────────────────┘                └─────────┬───────┘
                                             │
                                   ┌─────────────────┐
                                   │     Agent       │ (进一步扩展)
                                   │   (Satellite)   │
                                   └─────────────────┘
```

**Multi-AP 的核心功能**:

```c
// Multi-AP 功能模块
struct multi_ap_functions {
    // 1. 统一网络管理
    struct network_management {
        bool centralized_control;        // 集中控制
        bool unified_ssid;               // 统一 SSID
        bool seamless_roaming;           // 无缝漫游
        bool load_balancing;             // 负载均衡
    } network_mgmt;

    // 2. 智能客户端管理
    struct client_management {
        bool band_steering;              // 频段引导
        bool ap_steering;                // AP 引导
        bool airtime_fairness;           // 空口时间公平性
        bool client_blocking;            // 客户端阻塞
    } client_mgmt;

    // 3. 回程优化
    struct backhaul_optimization {
        bool dynamic_path_selection;     // 动态路径选择
        bool load_aware_routing;         // 负载感知路由
        bool redundancy_support;         // 冗余支持
        bool qos_management;             // QoS 管理
    } backhaul_opt;

    // 4. 自动化配置
    struct auto_configuration {
        bool zero_touch_provisioning;    // 零接触配置
        bool automatic_optimization;     // 自动优化
        bool self_healing;               // 自愈能力
        bool firmware_management;        // 固件管理
    } auto_config;
};
```

#### 1.2.2 Multi-AP 解决的关键问题

**1. 覆盖范围问题**
```
单 AP 覆盖限制:
┌─────────────────────────────────────┐
│              房屋平面图              │
│  ┌─────┐  ┌─────┐  ┌─────┐  ┌─────┐ │
│  │房间1│  │房间2│  │房间3│  │房间4│ │
│  │ ✓  │  │ ✓  │  │ ✗  │  │ ✗  │ │ (✗ = 信号弱)
│  └─────┘  └─────┘  └─────┘  └─────┘ │
│           [AP]                      │
└─────────────────────────────────────┘

Multi-AP 覆盖优化:
┌─────────────────────────────────────┐
│              房屋平面图              │
│  ┌─────┐  ┌─────┐  ┌─────┐  ┌─────┐ │
│  │房间1│  │房间2│  │房间3│  │房间4│ │
│  │ ✓  │  │ ✓  │  │ ✓  │  │ ✓  │ │ (全覆盖)
│  └─────┘  └─────┘  └─────┘  └─────┘ │
│    [AP1]    [AP2]    [AP3]          │
└─────────────────────────────────────┘
```

**2. 容量和性能问题**
```c
// 容量分析对比
struct capacity_analysis {
    // 单 AP 场景
    struct single_ap {
        uint32_t max_clients;            // 最大客户端数: ~50
        uint32_t total_bandwidth;        // 总带宽: 1200 Mbps
        uint32_t per_client_bandwidth;   // 每客户端带宽: 24 Mbps
        float    congestion_level;       // 拥塞水平: 高
    } single;

    // Multi-AP 场景
    struct multi_ap {
        uint32_t total_aps;              // AP 数量: 3
        uint32_t max_clients;            // 最大客户端数: 150
        uint32_t total_bandwidth;        // 总带宽: 3600 Mbps
        uint32_t per_client_bandwidth;   // 每客户端带宽: 72 Mbps
        float    congestion_level;       // 拥塞水平: 低
    } multi;
};
```

#### 1.2.3 EasyMesh 网络架构详解

```
EasyMesh 完整网络架构:
                    Internet
                        │
                ┌───────────────┐
                │   Controller  │ ◄─── Multi-AP 主控制器
                │   (Gateway)   │      • 网络策略制定
                └───────┬───────┘      • 拓扑管理
                        │              • 客户端协调
                        │ Backhaul     • 性能监控
                ┌───────────────┐
                │     Agent     │ ◄─── Multi-AP 代理节点
                │  (Satellite)  │      • 接收控制指令
                └───────┬───────┘      • 执行网络配置
                        │              • 上报状态信息
                        │ Backhaul     • 本地优化
                ┌───────────────┐
                │     Agent     │ ◄─── Multi-AP 代理节点
                │  (Satellite)  │      • 扩展网络覆盖
                └───────────────┘      • 协同工作

每个节点的 Multi-AP 功能:
┌─────────────────────────────────────────┐
│              Controller                 │
├─────────────────────────────────────────┤
│ • 全局网络视图                          │
│ • 客户端关联决策                        │
│ • 负载均衡策略                          │
│ • 漫游优化控制                          │
│ • 故障检测和恢复                        │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│               Agent                     │
├─────────────────────────────────────────┤
│ • 本地客户端管理                        │
│ • 信号质量监控                          │
│ • 频段引导执行                          │
│ • 回程链路维护                          │
│ • 配置同步                              │
└─────────────────────────────────────────┘
```

### 1.3 技术标准基础
- **IEEE 1905.1**: 融合网络抽象层协议 [Convergent Digital Home Network]
- **Wi-Fi Multi-AP**: Wi-Fi 联盟多接入点规范
- **IEEE 802.11k/v/r**: 无线网络管理和漫游优化
- **IEEE 802.11s**: 无线网状网络标准 (部分借鉴)

## 2. 协议栈架构

### 2.1 协议层次结构
```
EasyMesh 协议栈:
┌─────────────────────────────────────────┐
│           应用层 (Application)           │
│  - 网络管理应用                         │
│  - 配置管理界面                         │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│        Multi-AP 协议层 (Multi-AP)        │
│  - AP 自动配置协议 (ALMP)               │
│  - 客户端关联控制                       │
│  - 负载均衡和频段引导                   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│       IEEE 1905.1 抽象层 (AL)           │
│  - 拓扑发现协议                         │
│  - 链路度量收集                         │
│  - 消息路由和转发                       │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│         物理接口层 (Interfaces)          │
│  - Wi-Fi (802.11)                      │
│  - Ethernet (802.3)                    │
│  - MoCA, Powerline 等                  │
└─────────────────────────────────────────┘
```

### 2.2 IEEE 1905.1 核心机制

#### 2.2.1 抽象层 (Abstraction Layer)
**功能**: 为上层应用提供统一的网络接口，屏蔽底层物理介质差异

**关键特性**:
- **EtherType**: 0x893A (IEEE 1905.1 专用以太网类型)
- **组播地址**: 01:80:C2:00:00:13 (固定组播地址)
- **设备标识**: AL MAC 地址 (抽象层 MAC 地址)

#### 2.2.2 拓扑发现协议 (Topology Discovery)
```
拓扑发现消息流:
Device A                    Device B                    Device C
   │                          │                          │
   │──── Topology Discovery ──►│                          │
   │     (组播 0x893A)          │                          │
   │                          │──── Topology Discovery ──►│
   │                          │     (转发)                 │
   │◄─── Topology Response ───│                          │
   │                          │◄─── Topology Response ───│
   │                          │     (转发)                 │
   │                          │                          │
```

**拓扑发现消息格式**:
```c
struct topology_discovery_msg {
    uint8_t  al_mac_address[6];      // 发送设备的 AL MAC
    uint8_t  message_type;           // 消息类型 (0x00 = Discovery)
    uint16_t message_id;             // 消息 ID
    uint8_t  fragment_id;            // 分片 ID
    uint8_t  last_fragment;          // 最后分片标志
    // TLV 数据
    struct tlv_header tlvs[];        // 类型-长度-值数据
};
```

## 3. 无线组网机制

### 3.1 无线回程 (Wireless Backhaul) 实现

#### 3.1.1 频段分配策略
```
双频/三频设备频段分配:
┌─────────────────────────────────────────┐
│              5GHz-2 (专用回程)           │
│  - 专门用于 AP 间通信                   │
│  - 高带宽，低延迟                       │
│  - 动态信道选择                         │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              5GHz-1 (前端)              │
│  - 客户端接入                           │
│  - 高速数据传输                         │
│  - 负载均衡                             │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              2.4GHz (前端)              │
│  - 客户端接入                           │
│  - 兼容性和覆盖范围                     │
│  - IoT 设备支持                         │
└─────────────────────────────────────────┘
```

#### 3.1.2 无线链路建立过程

**是的，EasyMesh 使用 WPS (Wi-Fi Protected Setup) 协议进行无线链路的自动建立**

##### 3.1.2.1 WPS 在 EasyMesh 中的应用
```
EasyMesh 中的 WPS 应用:
┌─────────────────────────────────────────┐
│              WPS 角色分配                │
├─────────────────────────────────────────┤
│ Controller (主控制器):                  │
│ - 充当 WPS Registrar (注册器)           │
│ - 管理网络凭据和配置                    │
│ - 向 Agent 分发配置信息                 │
│                                         │
│ Agent (网格节点):                       │
│ - 充当 WPS Enrollee (注册者)            │
│ - 接收来自 Controller 的配置            │
│ - 自动配置无线参数                      │
└─────────────────────────────────────────┘
```

##### 3.1.2.2 详细的 WPS 握手过程
```
完整的 WPS 自动配置流程:
Controller (Registrar)                    Agent (Enrollee)
    │                                         │
    │──── AP Autoconfiguration Search ───────►│
    │     (Multi-AP 发现消息)                 │
    │                                         │
    │◄─── AP Autoconfiguration Response ──────│
    │     (Agent 响应，包含 WPS 能力)         │
    │                                         │
    │──── WSC M1 (Hello) ────────────────────►│
    │     - Enrollee Nonce                    │
    │     - Public Key                        │
    │     - Device Info                       │
    │                                         │
    │◄─── WSC M2 (Hello Response) ────────────│
    │     - Registrar Nonce                   │
    │     - Public Key                        │
    │     - Encrypted Settings                │
    │                                         │
    │──── WSC M3 (Identity) ─────────────────►│
    │     - E-Hash1 (身份验证哈希)            │
    │                                         │
    │◄─── WSC M4 (Identity Response) ─────────│
    │     - R-Hash1                           │
    │                                         │
    │──── WSC M5 (Credential) ───────────────►│
    │     - E-Hash2                           │
    │     - Encrypted E-S1                    │
    │                                         │
    │◄─── WSC M6 (Credential Response) ───────│
    │     - R-Hash2                           │
    │     - Encrypted R-S1                    │
    │                                         │
    │──── WSC M7 (Settings) ─────────────────►│
    │     - Encrypted Settings                │
    │     - Network Key (PSK)                 │
    │     - SSID Configuration                │
    │     - Security Settings                 │
    │                                         │
    │◄─── WSC M8 (Settings Response) ─────────│
    │     - Encrypted Done                    │
    │                                         │
    │──── WSC Done ──────────────────────────►│
    │                                         │
    │◄─── WSC ACK ────────────────────────────│
    │                                         │
    │──── 1905.1 拓扑发现 ───────────────────►│
    │                                         │
    │◄─── 拓扑响应 ───────────────────────────│
```

##### 3.1.2.3 WPS 消息结构详解
```c
// WSC (Wi-Fi Simple Configuration) 消息结构
struct wsc_message {
    uint8_t  version;                    // WPS 版本
    uint8_t  message_type;               // 消息类型 (M1-M8)
    uint16_t length;                     // 消息长度

    // 通用属性
    struct wsc_attribute {
        uint16_t type;                   // 属性类型
        uint16_t length;                 // 属性长度
        uint8_t  data[];                 // 属性数据
    } attributes[];
};

// M1 消息 (Enrollee Hello)
struct wsc_m1_message {
    uint8_t  version;                    // 0x10
    uint8_t  message_type;               // 0x04 (M1)
    uint8_t  uuid_enrollee[16];          // Enrollee UUID
    uint8_t  mac_address[6];             // MAC 地址
    uint8_t  enrollee_nonce[16];         // 随机数
    uint8_t  public_key[192];            // DH 公钥
    uint16_t auth_type_flags;            // 认证类型标志
    uint16_t encr_type_flags;            // 加密类型标志
    uint8_t  conn_type_flags;            // 连接类型标志
    uint16_t config_methods;             // 配置方法
    uint8_t  wps_state;                  // WPS 状态
    uint8_t  manufacturer[64];           // 制造商信息
    uint8_t  model_name[32];             // 型号名称
    uint8_t  model_number[32];           // 型号编号
    uint8_t  serial_number[32];          // 序列号
    uint8_t  primary_device_type[8];     // 主设备类型
    uint8_t  device_name[32];            // 设备名称
    uint16_t rf_bands;                   // RF 频段
    uint16_t assoc_state;                // 关联状态
    uint16_t device_password_id;         // 设备密码 ID
    uint16_t config_error;               // 配置错误
    uint32_t os_version;                 // 操作系统版本
};

// M2 消息 (Registrar Hello)
struct wsc_m2_message {
    uint8_t  version;                    // 0x10
    uint8_t  message_type;               // 0x05 (M2)
    uint8_t  enrollee_nonce[16];         // 来自 M1 的随机数
    uint8_t  registrar_nonce[16];        // Registrar 随机数
    uint8_t  uuid_registrar[16];         // Registrar UUID
    uint8_t  public_key[192];            // DH 公钥
    uint16_t auth_type_flags;            // 支持的认证类型
    uint16_t encr_type_flags;            // 支持的加密类型
    uint8_t  conn_type_flags;            // 连接类型
    uint16_t config_methods;             // 配置方法
    uint8_t  manufacturer[64];           // 制造商信息
    uint8_t  model_name[32];             // 型号名称
    uint8_t  model_number[32];           // 型号编号
    uint8_t  serial_number[32];          // 序列号
    uint8_t  primary_device_type[8];     // 主设备类型
    uint8_t  device_name[32];            // 设备名称
    uint16_t rf_bands;                   // RF 频段
    uint16_t assoc_state;                // 关联状态
    uint16_t config_error;               // 配置错误
    uint32_t os_version;                 // 操作系统版本
};
```

##### 3.1.2.4 EasyMesh 特有的 WPS 扩展
```c
// EasyMesh 专用的 WPS 属性
enum easymesh_wps_attributes {
    ATTR_MULTI_AP_EXTENSION         = 0x1067,  // Multi-AP 扩展
    ATTR_BACKHAUL_BSS_CONFIG        = 0x1068,  // 回程 BSS 配置
    ATTR_FRONTHAUL_BSS_CONFIG       = 0x1069,  // 前端 BSS 配置
    ATTR_TEARDOWN_INDICATION        = 0x106A,  // 拆除指示
    ATTR_PROFILE_VERSION            = 0x106B   // 配置文件版本
};

// Multi-AP 扩展属性
struct multi_ap_extension {
    uint8_t  subelement_id;              // 子元素 ID
    uint8_t  length;                     // 长度
    uint8_t  multi_ap_profile;           // Multi-AP 配置文件
    uint8_t  backhaul_sta_disallowed;    // 禁止回程 STA
    uint8_t  backhaul_bss_disallowed;    // 禁止回程 BSS
    uint8_t  fronthaul_bss_disallowed;   // 禁止前端 BSS
};

// 回程 BSS 配置
struct backhaul_bss_config {
    uint8_t  ssid[32];                   // 回程 SSID
    uint8_t  ssid_length;                // SSID 长度
    uint16_t auth_type;                  // 认证类型
    uint16_t encr_type;                  // 加密类型
    uint8_t  network_key[64];            // 网络密钥
    uint8_t  network_key_length;         // 密钥长度
    uint8_t  mac_address[6];             // MAC 地址
};
```

##### ******* 安全机制
```c
// WPS 安全机制
struct wps_security {
    // DH 密钥交换
    uint8_t  dh_private_key[192];        // DH 私钥
    uint8_t  dh_public_key[192];         // DH 公钥
    uint8_t  shared_secret[192];         // 共享密钥

    // 密钥派生
    uint8_t  auth_key[32];               // 认证密钥
    uint8_t  key_wrap_key[16];           // 密钥包装密钥
    uint8_t  emsk[32];                   // 扩展主会话密钥

    // PIN 验证
    uint8_t  psk1[16];                   // PIN 第一部分
    uint8_t  psk2[16];                   // PIN 第二部分
    uint8_t  e_hash1[32];                // E-Hash1
    uint8_t  e_hash2[32];                // E-Hash2
    uint8_t  r_hash1[32];                // R-Hash1
    uint8_t  r_hash2[32];                // R-Hash2
};

// 密钥派生函数 (KDF)
void derive_wps_keys(struct wps_security *sec,
                    uint8_t *shared_secret,
                    uint8_t *nonce_enrollee,
                    uint8_t *nonce_registrar) {
    uint8_t kdk[32];  // 密钥派生密钥

    // 1. 计算 KDK
    hmac_sha256(shared_secret, 192,
               "Wi-Fi Easy and Secure Key Derivation",
               strlen("Wi-Fi Easy and Secure Key Derivation"),
               kdk);

    // 2. 派生认证密钥
    kdf(kdk, 32, "WPS Key Derivation", nonce_enrollee, 16,
        nonce_registrar, 16, sec->auth_key, 32);

    // 3. 派生密钥包装密钥
    kdf(kdk, 32, "WPS Key Derivation", nonce_enrollee, 16,
        nonce_registrar, 16, sec->key_wrap_key, 16);

    // 4. 派生 EMSK
    kdf(kdk, 32, "WPS Key Derivation", nonce_enrollee, 16,
        nonce_registrar, 16, sec->emsk, 32);
}
```

##### 3.1.2.6 WPS 触发方式

**EasyMesh 支持多种 WPS 触发方式**:

```c
// WPS 触发方式
enum wps_trigger_method {
    WPS_METHOD_PBC,                      // 按钮配置 (Push Button Configuration)
    WPS_METHOD_PIN,                      // PIN 配置
    WPS_METHOD_NFC,                      // NFC 配置
    WPS_METHOD_AUTO                      // 自动配置 (EasyMesh 特有)
};

// 按钮配置 (PBC) 流程
struct pbc_session {
    uint32_t session_id;                 // 会话 ID
    uint32_t start_time;                 // 开始时间
    uint32_t timeout;                    // 超时时间 (120秒)
    uint8_t  uuid_enrollee[16];          // Enrollee UUID
    bool     session_active;             // 会话是否活跃
    bool     overlap_detected;           // 是否检测到重叠
};

// PBC 重叠检测
bool detect_pbc_overlap(struct pbc_session *sessions, int count) {
    uint32_t current_time = get_current_time();
    int active_sessions = 0;

    for (int i = 0; i < count; i++) {
        if (sessions[i].session_active &&
            (current_time - sessions[i].start_time) < sessions[i].timeout) {
            active_sessions++;
        }
    }

    // 如果有多个活跃会话，则检测到重叠
    return (active_sessions > 1);
}
```

**1. 按钮配置 (PBC) - 最常用方式**
```
PBC 配置流程:
用户操作                    Controller                    Agent
    │                          │                          │
    │ 按下 WPS 按钮             │                          │
    │ ────────────────────────► │                          │
    │                          │ 启动 PBC 会话             │
    │                          │ 广播 PBC 信标             │
    │                          │ ────────────────────────► │
    │                          │                          │ 用户按下 WPS 按钮
    │                          │                          │ ◄────────────────
    │                          │                          │ 启动 PBC 响应
    │                          │ ◄──── PBC 响应 ──────────│
    │                          │                          │
    │                          │ ──── WPS 握手开始 ──────► │
    │                          │      (M1-M8 消息)        │
    │                          │ ◄─── 配置完成 ───────────│
```

**2. PIN 配置 - 企业环境常用**
```c
// PIN 配置
struct pin_config {
    uint8_t  device_pin[8];              // 设备 PIN (8位数字)
    uint32_t pin_checksum;               // PIN 校验和
    uint32_t pin_attempts;               // PIN 尝试次数
    uint32_t lockout_time;               // 锁定时间
    bool     pin_locked;                 // PIN 是否被锁定
};

// PIN 验证算法
bool validate_pin(struct pin_config *config, uint8_t *input_pin) {
    // 1. 检查 PIN 是否被锁定
    if (config->pin_locked) {
        uint32_t current_time = get_current_time();
        if (current_time < config->lockout_time) {
            return false;  // 仍在锁定期内
        } else {
            config->pin_locked = false;  // 解除锁定
            config->pin_attempts = 0;
        }
    }

    // 2. 验证 PIN 校验和
    uint32_t calculated_checksum = calculate_pin_checksum(input_pin);
    if (calculated_checksum != config->pin_checksum) {
        config->pin_attempts++;

        // 3. 检查是否需要锁定
        if (config->pin_attempts >= MAX_PIN_ATTEMPTS) {
            config->pin_locked = true;
            config->lockout_time = get_current_time() + PIN_LOCKOUT_DURATION;
        }

        return false;
    }

    // 4. PIN 验证成功
    config->pin_attempts = 0;
    return true;
}
```

**3. 自动配置 - EasyMesh 智能模式**
```c
// 自动配置模式
struct auto_config_mode {
    bool     enabled;                    // 是否启用自动配置
    uint32_t discovery_interval;        // 发现间隔 (秒)
    uint32_t max_attempts;               // 最大尝试次数
    uint8_t  trusted_oui_list[16][3];    // 信任的 OUI 列表
    uint8_t  security_policy;            // 安全策略
};

// 自动配置触发条件
bool should_trigger_auto_config(struct auto_config_mode *mode) {
    // 1. 检查是否启用自动配置
    if (!mode->enabled) return false;

    // 2. 检查网络环境
    if (has_existing_configuration()) return false;

    // 3. 检查信任的设备
    if (!detect_trusted_controller(mode->trusted_oui_list)) return false;

    // 4. 检查安全策略
    if (!meets_security_requirements(mode->security_policy)) return false;

    return true;
}
```

##### 3.1.2.7 为什么 WPS 要封装在 IEEE 1905.1 中？

**这是一个很好的问题！让我详细解释 WPS 封装的原因和机制：**

#### 问题背景分析

**传统 WPS 的局限性**:
```
传统 WPS 工作模式:
客户端设备                    单个 AP
     │                         │
     │──── WPS 握手 ───────────►│
     │     (直接 Wi-Fi 连接)    │
     │◄─── 配置信息 ────────────│
     │                         │
     └── 建立 Wi-Fi 连接 ──────►│

问题:
1. 只能配置单个 AP
2. 无法处理多 AP 网络
3. 缺乏网络拓扑感知
4. 无法协调多个节点
```

**EasyMesh 的需求**:
```
EasyMesh 网络配置需求:
Controller                Agent 1              Agent 2
    │                        │                    │
    │ 需要配置整个网络拓扑    │                    │
    │ 需要协调多个 Agent     │                    │
    │ 需要统一管理           │                    │
    │                        │                    │
    └── 不是简单的点对点连接 ──┘                    │
```

#### WPS 封装在 1905.1 的核心原因

**1. 网络拓扑感知**
```c
// 1905.1 提供网络拓扑抽象
struct network_topology_context {
    // WPS 需要知道网络中的所有设备
    struct device_list {
        uint8_t  al_mac_address[6];      // 设备的抽象层 MAC
        uint8_t  device_role;            // Controller 或 Agent
        uint32_t device_capabilities;    // 设备能力
        bool     wps_configured;         // WPS 配置状态
    } devices[MAX_DEVICES];

    // 传统 WPS 无法获得这些信息
    // 1905.1 通过拓扑发现提供全网视图
};

// 为什么需要拓扑感知？
void explain_topology_awareness() {
    /*
     * 在 EasyMesh 中，WPS 不是简单的两设备配置：
     *
     * 1. Controller 需要知道哪些 Agent 需要配置
     * 2. Agent 需要知道谁是 Controller
     * 3. 需要避免配置冲突
     * 4. 需要协调配置顺序
     * 5. 需要处理配置失败的恢复
     *
     * 这些都需要网络拓扑信息，而 1905.1 正好提供这个能力
     */
}
```

**2. 多接口统一管理**
```c
// 1905.1 抽象层的作用
struct interface_abstraction {
    // 设备可能有多个接口
    struct physical_interface {
        uint8_t  interface_mac[6];       // 接口 MAC
        uint16_t interface_type;         // 接口类型 (Wi-Fi/Ethernet)
        uint8_t  interface_role;         // 前端/回程
        bool     wps_capable;            // 是否支持 WPS
    } interfaces[MAX_INTERFACES];

    // 1905.1 提供统一的接口抽象
    uint8_t al_mac_address[6];           // 抽象层 MAC (设备唯一标识)
};

// WPS 配置需要指定具体接口
struct wps_interface_config {
    uint8_t  target_interface[6];        // 目标接口
    uint8_t  interface_role;             // 前端还是回程
    struct wps_config config;            // WPS 配置参数
};
```

**3. 消息路由和转发**
```c
// 1905.1 提供消息路由能力
struct message_routing_context {
    // WPS 消息可能需要跨多个跳转
    struct routing_path {
        uint8_t  source_al_mac[6];       // 源设备
        uint8_t  dest_al_mac[6];         // 目标设备
        uint8_t  next_hop_mac[6];        // 下一跳
        uint8_t  interface_mac[6];       // 出接口
    } routing_table[MAX_ROUTES];
};

// 为什么需要路由？
void explain_routing_need() {
    /*
     * 在复杂的 EasyMesh 网络中：
     *
     * Controller ──── Agent 1 ──── Agent 2
     *
     * Controller 要配置 Agent 2，但可能需要通过 Agent 1 转发
     * 传统 WPS 无法处理这种多跳场景
     * 1905.1 提供了消息路由和转发机制
     */
}
```

#### WPS 在 1905.1 中的封装机制

**1. 消息封装结构**
```c
// 完整的消息封装层次
struct easymesh_wps_message_stack {
    // 以太网层
    struct ethernet_header {
        uint8_t  dest_mac[6];            // 目标 MAC
        uint8_t  src_mac[6];             // 源 MAC
        uint16_t ethertype;              // 0x893A (1905.1)
    } eth_hdr;

    // 1905.1 抽象层
    struct ieee1905_header {
        uint8_t  al_mac_address[6];      // 抽象层 MAC
        uint8_t  message_version;        // 消息版本
        uint16_t message_type;           // AP_AUTOCONFIGURATION_WSC
        uint16_t message_id;             // 消息 ID
        uint8_t  fragment_id;            // 分片 ID
        uint8_t  indicators;             // 指示符
    } ieee1905_hdr;

    // WSC TLV 封装
    struct wsc_tlv {
        uint8_t  tlv_type;               // TLV 类型 (WSC = 0x0F)
        uint16_t tlv_length;             // TLV 长度

        // WPS 消息内容
        struct wps_message {
            uint8_t  wps_message_type;   // M1, M2, M3, ..., M8
            uint8_t  wps_data[];         // WPS 具体数据
        } wps_msg;
    } wsc_tlv;
};
```

**2. 配置协调机制**
```c
// 多设备配置协调
struct multi_device_wps_coordination {
    // 配置会话管理
    struct wps_session {
        uint8_t  session_id[16];         // 会话 ID
        uint8_t  controller_al_mac[6];   // Controller AL MAC
        uint8_t  agent_al_mac[6];        // Agent AL MAC
        uint8_t  target_interface[6];    // 目标接口
        uint32_t session_timeout;        // 会话超时
        enum wps_state current_state;    // 当前状态
    } sessions[MAX_WPS_SESSIONS];

    // 配置冲突避免
    bool prevent_wps_conflicts(struct wps_session *new_session) {
        for (int i = 0; i < MAX_WPS_SESSIONS; i++) {
            if (sessions[i].current_state != WPS_STATE_IDLE) {
                // 检查是否有冲突
                if (memcmp(sessions[i].agent_al_mac,
                          new_session->agent_al_mac, 6) == 0) {
                    return false;  // 同一设备已在配置中
                }
            }
        }
        return true;
    }
};
```

**3. 配置状态同步**
```c
// 配置状态在网络中的同步
struct wps_state_synchronization {
    // 配置状态广播
    void broadcast_wps_state(uint8_t agent_al_mac[6],
                           enum wps_state state) {
        struct ieee1905_message msg;
        msg.message_type = TOPOLOGY_NOTIFICATION_MESSAGE;

        // 添加 WPS 状态 TLV
        struct wps_state_tlv {
            uint8_t  tlv_type;           // WPS_STATE_TLV
            uint16_t tlv_length;
            uint8_t  agent_al_mac[6];
            uint8_t  wps_state;
            uint32_t timestamp;
        } wps_tlv;

        // 通过 1905.1 网络广播
        send_1905_message_to_all_neighbors(&msg);
    }

    // 其他设备接收状态更新
    void handle_wps_state_update(struct wps_state_tlv *tlv) {
        // 更新本地拓扑数据库
        update_device_wps_state(tlv->agent_al_mac, tlv->wps_state);

        // 如果是 Controller，可能需要调整配置策略
        if (is_controller()) {
            adjust_wps_strategy_based_on_state(tlv);
        }
    }
};
```

##### 3.1.2.8 WPS 与 1905.1 集成的实际工作流程

**完整的 EasyMesh 设备加入流程**:

```
阶段 1: 拓扑发现 (使用 1905.1)
Controller                    New Agent
    │                            │
    │ 1. 定期广播拓扑发现消息      │
    │ ────────────────────────────►│
    │    (EtherType 0x893A)       │
    │                            │ 2. 新设备检测到网络
    │◄─── 拓扑响应 ───────────────│
    │    (包含设备能力信息)        │
    │                            │
    │ 3. 识别为未配置的 Agent     │
    │    决定启动 WPS 配置        │

阶段 2: WPS 配置协商 (通过 1905.1 传输)
Controller                    New Agent
    │                            │
    │ 4. 发送 AP 自动配置搜索     │
    │ ────────────────────────────►│
    │    (1905.1 消息类型 0x0007) │
    │    包含 WPS 搜索参数        │
    │                            │
    │◄─── AP 自动配置响应 ────────│
    │    (1905.1 消息类型 0x0008) │
    │    包含 WPS 能力信息        │
    │                            │
    │ 5. 开始 WPS 握手过程        │

阶段 3: WPS 消息交换 (封装在 1905.1 中)
Controller                    New Agent
    │                            │
    │ 6. WSC M1 (封装在 1905.1)   │
    │ ────────────────────────────►│
    │    消息类型: 0x0009         │
    │    TLV 类型: WSC            │
    │                            │
    │◄─── WSC M2 (封装在 1905.1) ─│
    │                            │
    │ 7. WSC M3-M8 消息交换       │
    │ ◄──────────────────────────►│
    │    (全部通过 1905.1 传输)   │
    │                            │
    │ 8. 配置信息传输完成         │

阶段 4: 配置应用和验证
Controller                    New Agent
    │                            │
    │                            │ 9. 应用接收到的配置
    │                            │    - SSID 和密钥
    │                            │    - 安全参数
    │                            │    - 角色配置
    │                            │
    │ 10. 验证配置是否成功        │
    │ ────────────────────────────►│
    │     (1905.1 拓扑查询)       │
    │                            │
    │◄─── 配置状态确认 ───────────│
    │     (包含新的网络配置)      │
    │                            │
    │ 11. 将设备加入拓扑数据库    │
    │     开始正常网络管理        │
```

**关键技术细节**:

```c
// 实际的消息处理流程
void handle_easymesh_device_onboarding(uint8_t new_device_mac[6]) {
    // 1. 拓扑发现阶段
    struct topology_discovery_result result;
    if (discover_new_device(new_device_mac, &result)) {

        // 2. 检查设备是否需要配置
        if (result.device_state == DEVICE_STATE_UNCONFIGURED) {

            // 3. 启动 WPS 配置流程
            struct wps_session session;
            init_wps_session(&session, new_device_mac);

            // 4. 发送 AP 自动配置搜索 (通过 1905.1)
            struct ieee1905_message search_msg;
            search_msg.message_type = AP_AUTOCONFIGURATION_SEARCH_MESSAGE;

            // 添加 WPS 搜索 TLV
            struct ap_autoconfiguration_search_tlv search_tlv;
            search_tlv.searched_role = WPS_REGISTRAR;
            search_tlv.autoconfig_freq_band = FREQ_BAND_2_4_5_GHZ;

            add_tlv_to_message(&search_msg, &search_tlv);
            send_1905_message(new_device_mac, &search_msg);

            // 5. 等待响应并处理 WPS 握手
            wait_for_wps_response(&session);
        }
    }
}

// WPS 消息的 1905.1 封装处理
void send_wps_message_via_1905(uint8_t dest_al_mac[6],
                               struct wps_message *wps_msg) {
    // 创建 1905.1 消息
    struct ieee1905_message msg;
    msg.message_type = AP_AUTOCONFIGURATION_WSC_MESSAGE;
    msg.message_id = generate_message_id();
    memcpy(msg.al_mac_address, get_local_al_mac(), 6);

    // 创建 WSC TLV
    struct wsc_tlv tlv;
    tlv.tlv_type = TLV_TYPE_WSC;
    tlv.tlv_length = wps_msg->length;
    memcpy(tlv.wsc_data, wps_msg->data, wps_msg->length);

    // 添加 TLV 到消息
    add_tlv_to_message(&msg, &tlv);

    // 通过 1905.1 网络发送
    send_1905_message(dest_al_mac, &msg);
}
```

**总结：WPS 封装在 1905.1 的必要性**

1. **网络拓扑感知**: 1905.1 提供全网设备发现和拓扑管理
2. **多接口抽象**: 统一管理设备的多个网络接口
3. **消息路由**: 支持多跳网络中的消息传输
4. **配置协调**: 避免多设备同时配置的冲突
5. **状态同步**: 在整个网络中同步配置状态
6. **扩展性**: 支持未来的协议扩展和增强

**简单来说**: 传统 WPS 只能处理两个设备间的配置，而 EasyMesh 需要在复杂的多设备网络中协调配置，因此需要 1905.1 提供的网络抽象和管理能力。

##### 3.1.2.9 原始 WPS 包结构 vs EasyMesh 中的 WPS

**您说得完全正确！WPS (2007年) 确实早于 IEEE 1905.1 (2013年) 出现。**

#### 原始 WPS 的包结构

**1. 传统 WPS over Wi-Fi 的包结构**
```
原始 WPS 包结构 (IEEE 802.11):
┌─────────────────────────────────────────────────────────┐
│                802.11 MAC Header                        │
├─────────────────────────────────────────────────────────┤
│ Frame Control │ Duration │ Address 1-4 │ Seq Control   │
│ (2 bytes)     │ (2 bytes)│ (24 bytes)  │ (2 bytes)     │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                802.11 Management Frame                  │
├─────────────────────────────────────────────────────────┤
│ Action Category │ Action Code │ Dialog Token │ Elements │
│ (1 byte)        │ (1 byte)    │ (1 byte)     │ (Variable)│
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                WPS Information Element                   │
├─────────────────────────────────────────────────────────┤
│ Element ID │ Length │ OUI │ OUI Type │ WPS Attributes   │
│ (0xDD)     │ (1 byte)│(3B) │ (1 byte) │ (Variable)      │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                WPS Attributes (TLV Format)              │
├─────────────────────────────────────────────────────────┤
│ Attribute Type │ Attribute Length │ Attribute Value    │
│ (2 bytes)      │ (2 bytes)        │ (Variable)         │
└─────────────────────────────────────────────────────────┘
```

**2. 原始 WPS 的传输方式**
```c
// 原始 WPS 传输方式
enum original_wps_transport {
    WPS_TRANSPORT_EAPOL,                 // EAP over LAN (最常用)
    WPS_TRANSPORT_UPNP,                  // UPnP (外部注册器)
    WPS_TRANSPORT_NFC,                   // 近场通信
    WPS_TRANSPORT_USB                    // USB 传输
};

// EAP-WSC 包结构 (最常用的 WPS 传输方式)
struct eap_wsc_packet {
    // 以太网头
    struct ethernet_header {
        uint8_t  dest_mac[6];            // 目标 MAC
        uint8_t  src_mac[6];             // 源 MAC
        uint16_t ethertype;              // 0x888E (EAP over LAN)
    } eth_hdr;

    // EAPOL 头
    struct eapol_header {
        uint8_t  version;                // EAPOL 版本
        uint8_t  type;                   // EAPOL 类型 (EAP Packet)
        uint16_t length;                 // 包长度
    } eapol_hdr;

    // EAP 头
    struct eap_header {
        uint8_t  code;                   // Request/Response/Success/Failure
        uint8_t  identifier;             // 标识符
        uint16_t length;                 // EAP 长度
        uint8_t  type;                   // EAP-WSC (0xFE)
    } eap_hdr;

    // WSC 数据
    struct wsc_data {
        uint8_t  vendor_id[3];           // 厂商 ID (0x00372A)
        uint32_t vendor_type;            // 厂商类型 (WPS = 1)
        uint8_t  opcode;                 // 操作码 (WSC_Start, WSC_ACK, etc.)
        uint8_t  flags;                  // 标志位
        uint8_t  message_data[];         // WSC 消息数据 (M1-M8)
    } wsc_data;
};
```

**3. 原始 WPS 的工作流程**
```
传统 WPS 工作流程 (点对点):
Enrollee (客户端)              Registrar (AP)
       │                           │
       │ 1. 802.11 Association      │
       │ ──────────────────────────►│
       │                           │
       │ 2. EAPOL-Start             │
       │ ──────────────────────────►│
       │                           │
       │◄─── 3. EAP-Request/Identity │
       │                           │
       │ 4. EAP-Response/Identity   │
       │    (WSC_ID)                │
       │ ──────────────────────────►│
       │                           │
       │◄─── 5. EAP-Request/WSC ────│
       │    (WSC_Start)             │
       │                           │
       │ 6. EAP-Response/WSC        │
       │    (M1 消息)               │
       │ ──────────────────────────►│
       │                           │
       │◄─── 7. EAP-Request/WSC ────│
       │    (M2 消息)               │
       │                           │
       │ 8-15. M3-M8 消息交换       │
       │ ◄─────────────────────────►│
       │                           │
       │◄─── 16. EAP-Success ───────│
       │                           │
       │ 17. 使用新凭据重新关联     │
       │ ──────────────────────────►│
```

#### EasyMesh 中的包结构变化

**1. EasyMesh 无线组网包结构**
```
EasyMesh 无线组网包结构:
┌─────────────────────────────────────────────────────────┐
│                802.11 MAC Header                        │ ← 底层相同
├─────────────────────────────────────────────────────────┤
│                802.11 Data Frame                        │ ← 使用数据帧而非管理帧
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                Ethernet Header                          │
├─────────────────────────────────────────────────────────┤
│ Dest MAC │ Src MAC │ EtherType (0x893A)                │ ← 1905.1 专用
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                IEEE 1905.1 Header                       │ ← 新增抽象层
├─────────────────────────────────────────────────────────┤
│ AL MAC │ Version │ Type │ ID │ Fragment │ Indicators    │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                WSC TLV                                  │ ← WPS 封装在 TLV 中
├─────────────────────────────────────────────────────────┤
│ TLV Type │ TLV Length │ WSC Data (M1-M8)               │
└─────────────────────────────────────────────────────────┘
```

**2. EasyMesh 有线组网包结构**
```
EasyMesh 有线组网包结构:
┌─────────────────────────────────────────────────────────┐
│                Ethernet Header                          │ ← 底层不同
├─────────────────────────────────────────────────────────┤
│ Dest MAC │ Src MAC │ EtherType (0x893A)                │ ← 1905.1 专用
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                IEEE 1905.1 Header                       │ ← 抽象层相同
├─────────────────────────────────────────────────────────┤
│ AL MAC │ Version │ Type │ ID │ Fragment │ Indicators    │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                WSC TLV                                  │ ← 上层完全相同
├─────────────────────────────────────────────────────────┤
│ TLV Type │ TLV Length │ WSC Data (M1-M8)               │
└─────────────────────────────────────────────────────────┘
```

#### 关键差异对比

```c
// 包结构对比
struct packet_structure_comparison {
    // 原始 WPS
    struct original_wps {
        char transport_layer[32];        // "802.11 Management" 或 "EAPOL"
        char wps_encapsulation[32];      // "直接 WSC 消息"
        bool topology_awareness;         // false
        bool multi_device_support;       // false
        char scope[32];                  // "点对点配置"
    } original;

    // EasyMesh WPS
    struct easymesh_wps {
        char transport_layer[32];        // "802.11 Data + 1905.1" 或 "Ethernet + 1905.1"
        char wps_encapsulation[32];      // "1905.1 TLV 封装"
        bool topology_awareness;         // true
        bool multi_device_support;       // true
        char scope[32];                  // "网络级配置"
    } easymesh;
};
```

**回答您的问题**:

1. **原始 WPS 包结构**: 使用 EAP-WSC over EAPOL 或 802.11 管理帧，直接传输 WSC 消息
2. **EasyMesh 无线 vs 有线**:
   - **底层不同**: 无线使用 802.11，有线使用以太网
   - **抽象层相同**: 都使用 IEEE 1905.1 抽象层
   - **上层完全相同**: WSC TLV 封装和 WPS 消息内容完全一致

**核心优势**: 1905.1 抽象层使得无线和有线可以使用统一的上层协议，简化了多接口设备的实现。

##### 3.1.2.10 WPS M1-M8 消息详细解析

**注意**: WPS 标准只有 M1-M8 消息，没有 M9。让我详细解析每个消息的内容和作用：

#### M1 消息 (Enrollee Hello) - 设备身份介绍

**作用**: Enrollee (要配置的设备) 向 Registrar (配置器) 介绍自己

```c
// M1 消息详细结构
struct wps_m1_message {
    // 消息头
    uint8_t  version;                    // WPS 版本 (0x10)
    uint8_t  message_type;               // 0x04 (M1)

    // 设备身份信息
    uint8_t  uuid_enrollee[16];          // 设备唯一标识符
    uint8_t  mac_address[6];             // 设备 MAC 地址
    uint8_t  enrollee_nonce[16];         // 随机数 (防重放攻击)
    uint8_t  public_key[192];            // DH 公钥 (1536位)

    // 认证和加密能力
    uint16_t auth_type_flags;            // 支持的认证类型
    /*
     * 0x0001 = Open
     * 0x0002 = WPA-Personal
     * 0x0004 = Shared
     * 0x0008 = WPA-Enterprise
     * 0x0010 = WPA2-Personal
     * 0x0020 = WPA2-Enterprise
     * 0x0040 = WPA3-Personal
     */

    uint16_t encr_type_flags;            // 支持的加密类型
    /*
     * 0x0001 = None
     * 0x0002 = WEP
     * 0x0004 = TKIP
     * 0x0008 = AES
     * 0x0010 = AES-TKIP (混合模式)
     */

    uint8_t  conn_type_flags;            // 连接类型
    /*
     * 0x01 = ESS (基础设施模式)
     * 0x02 = IBSS (Ad-hoc 模式)
     */

    uint16_t config_methods;             // 支持的配置方法
    /*
     * 0x0001 = USBA (USB-A)
     * 0x0002 = Ethernet
     * 0x0004 = Label
     * 0x0008 = Display
     * 0x0010 = External NFC Token
     * 0x0020 = Integrated NFC Token
     * 0x0040 = NFC Interface
     * 0x0080 = Push Button
     * 0x0100 = Keypad
     * 0x0280 = Virtual Push Button
     * 0x2008 = Physical Display
     * 0x4008 = Virtual Display
     */

    uint8_t  wps_state;                  // WPS 状态
    /*
     * 0x01 = Not Configured
     * 0x02 = Configured
     */

    // 设备信息
    uint8_t  manufacturer[64];           // 制造商名称
    uint8_t  model_name[32];             // 型号名称
    uint8_t  model_number[32];           // 型号编号
    uint8_t  serial_number[32];          // 序列号
    uint8_t  primary_device_type[8];     // 主设备类型
    /*
     * 格式: Category ID (2B) + OUI (4B) + Sub Category ID (2B)
     * 例如: 0x0006 0x0050F204 0x0001 = Network Infrastructure, Router
     */

    uint8_t  device_name[32];            // 设备名称
    uint16_t rf_bands;                   // 支持的 RF 频段
    /*
     * 0x01 = 2.4 GHz
     * 0x02 = 5.0 GHz
     * 0x03 = 2.4 + 5.0 GHz
     */

    uint16_t assoc_state;                // 关联状态
    /*
     * 0x00 = Not Associated
     * 0x01 = Connection Success
     * 0x02 = Configuration Failure
     * 0x03 = Association Failure
     * 0x04 = IP Failure
     */

    uint16_t device_password_id;         // 设备密码 ID
    /*
     * 0x0000 = Default (PIN)
     * 0x0001 = User-specified
     * 0x0002 = Machine-specified
     * 0x0003 = Rekey
     * 0x0004 = Push Button
     * 0x0005 = Registrar-specified
     */

    uint16_t config_error;               // 配置错误码
    uint32_t os_version;                 // 操作系统版本
};

// M1 消息示例内容
void example_m1_content() {
    printf("M1 消息内容示例:\n");
    printf("设备名称: TP-Link_Archer_AX73\n");
    printf("制造商: TP-Link Technologies Co., Ltd.\n");
    printf("型号: Archer AX73\n");
    printf("序列号: 21A1234567890\n");
    printf("支持频段: 2.4GHz + 5GHz\n");
    printf("配置方法: Push Button + Virtual Display\n");
    printf("认证类型: WPA2-Personal + WPA3-Personal\n");
    printf("加密类型: AES\n");
    printf("设备状态: Not Configured\n");
}
```

#### M2 消息 (Registrar Hello) - 配置器响应

**作用**: Registrar 向 Enrollee 确认配置能力并提供自己的信息

```c
// M2 消息详细结构
struct wps_m2_message {
    // 消息头
    uint8_t  version;                    // WPS 版本 (0x10)
    uint8_t  message_type;               // 0x05 (M2)

    // 回显 M1 信息
    uint8_t  enrollee_nonce[16];         // 来自 M1 的随机数

    // Registrar 信息
    uint8_t  registrar_nonce[16];        // Registrar 随机数
    uint8_t  uuid_registrar[16];         // Registrar UUID
    uint8_t  public_key[192];            // Registrar DH 公钥

    // 支持的能力 (通常是 M1 的子集)
    uint16_t auth_type_flags;            // Registrar 支持的认证类型
    uint16_t encr_type_flags;            // Registrar 支持的加密类型
    uint8_t  conn_type_flags;            // 连接类型
    uint16_t config_methods;             // Registrar 支持的配置方法

    // Registrar 设备信息
    uint8_t  manufacturer[64];           // Registrar 制造商
    uint8_t  model_name[32];             // Registrar 型号
    uint8_t  model_number[32];           // Registrar 型号编号
    uint8_t  serial_number[32];          // Registrar 序列号
    uint8_t  primary_device_type[8];     // Registrar 设备类型
    uint8_t  device_name[32];            // Registrar 设备名称
    uint16_t rf_bands;                   // Registrar RF 频段
    uint16_t assoc_state;                // 关联状态
    uint16_t config_error;               // 配置错误
    uint32_t os_version;                 // 操作系统版本
};

// M2 消息的关键作用
void m2_message_purpose() {
    printf("M2 消息的关键作用:\n");
    printf("1. 确认接受 Enrollee 的配置请求\n");
    printf("2. 提供 Registrar 的 DH 公钥\n");
    printf("3. 协商最终使用的认证和加密方法\n");
    printf("4. 建立共享密钥的基础\n");
}
```

#### M3 消息 (Enrollee Authenticator) - 身份验证开始

**作用**: Enrollee 提供第一个身份验证哈希

```c
// M3 消息详细结构
struct wps_m3_message {
    uint8_t  version;                    // WPS 版本
    uint8_t  message_type;               // 0x07 (M3)
    uint8_t  registrar_nonce[16];        // 来自 M2 的随机数
    uint8_t  e_hash1[32];                // E-Hash1 (身份验证哈希1)
    /*
     * E-Hash1 = HMAC-SHA256(AuthKey, E-S1 || PSK1 || PKE || PKR)
     * 其中:
     * - AuthKey: 从共享密钥派生的认证密钥
     * - E-S1: Enrollee Secret 1 (随机数)
     * - PSK1: PIN 的前半部分
     * - PKE: Enrollee 公钥
     * - PKR: Registrar 公钥
     */
};

// E-Hash1 计算过程
void calculate_e_hash1(struct wps_security *sec, uint8_t *e_hash1) {
    uint8_t input_buffer[256];
    int offset = 0;

    // 构造输入数据
    memcpy(input_buffer + offset, sec->e_s1, 16);
    offset += 16;
    memcpy(input_buffer + offset, sec->psk1, 16);
    offset += 16;
    memcpy(input_buffer + offset, sec->enrollee_public_key, 192);
    offset += 192;
    memcpy(input_buffer + offset, sec->registrar_public_key, 192);
    offset += 192;

    // 计算 HMAC-SHA256
    hmac_sha256(sec->auth_key, 32, input_buffer, offset, e_hash1);
}
```

#### M4 消息 (Registrar Authenticator) - 配置器验证

**作用**: Registrar 提供对应的身份验证哈希

```c
// M4 消息详细结构
struct wps_m4_message {
    uint8_t  version;                    // WPS 版本
    uint8_t  message_type;               // 0x08 (M4)
    uint8_t  enrollee_nonce[16];         // 来自 M1 的随机数
    uint8_t  r_hash1[32];                // R-Hash1 (Registrar 哈希1)
    /*
     * R-Hash1 = HMAC-SHA256(AuthKey, R-S1 || PSK1 || PKE || PKR)
     * 其中:
     * - R-S1: Registrar Secret 1 (随机数)
     * - 其他参数与 E-Hash1 相同
     */

    // 加密的 R-S1
    uint8_t  encrypted_r_s1[16];         // 使用 KeyWrapKey 加密的 R-S1
    /*
     * 加密算法: AES-128-CBC
     * 密钥: KeyWrapKey (从共享密钥派生)
     * IV: 固定值或随机值
     */
};
```

#### M5 消息 (Enrollee Credential) - 设备凭据

**作用**: Enrollee 提供第二个哈希并发送加密的密钥

```c
// M5 消息详细结构
struct wps_m5_message {
    uint8_t  version;                    // WPS 版本
    uint8_t  message_type;               // 0x09 (M5)
    uint8_t  registrar_nonce[16];        // 来自 M2 的随机数
    uint8_t  e_hash2[32];                // E-Hash2 (身份验证哈希2)
    /*
     * E-Hash2 = HMAC-SHA256(AuthKey, E-S2 || PSK2 || PKE || PKR)
     * PSK2 是 PIN 的后半部分
     */

    // 加密的 E-S1
    uint8_t  encrypted_e_s1[16];         // 使用 KeyWrapKey 加密的 E-S1
};
```

#### M6 消息 (Registrar Credential) - 配置器凭据

**作用**: Registrar 提供第二个哈希并发送加密的密钥

```c
// M6 消息详细结构
struct wps_m6_message {
    uint8_t  version;                    // WPS 版本
    uint8_t  message_type;               // 0x0A (M6)
    uint8_t  enrollee_nonce[16];         // 来自 M1 的随机数
    uint8_t  r_hash2[32];                // R-Hash2 (Registrar 哈希2)

    // 加密的 R-S2
    uint8_t  encrypted_r_s2[16];         // 使用 KeyWrapKey 加密的 R-S2
};
```

#### M7 消息 (Network Settings) - 网络配置传输

**作用**: Registrar 发送实际的网络配置信息 (最重要的消息)

```c
// M7 消息详细结构
struct wps_m7_message {
    uint8_t  version;                    // WPS 版本
    uint8_t  message_type;               // 0x0B (M7)
    uint8_t  registrar_nonce[16];        // 来自 M2 的随机数

    // 加密的 E-S2
    uint8_t  encrypted_e_s2[16];         // 使用 KeyWrapKey 加密的 E-S2

    // 加密的网络设置 (核心内容)
    struct encrypted_settings {
        uint16_t length;                 // 加密数据长度
        uint8_t  iv[16];                 // 初始化向量
        uint8_t  encrypted_data[];       // 加密的网络配置
        /*
         * 解密后包含:
         * - SSID
         * - Network Key (PSK)
         * - Authentication Type
         * - Encryption Type
         * - MAC Address
         * - Key Index (WEP 使用)
         * - Network Key Index
         */
    } settings;
};

// M7 中的网络配置内容
struct network_configuration {
    uint8_t  ssid[32];                   // 网络名称
    uint8_t  ssid_length;                // SSID 长度
    uint8_t  network_key[64];            // 网络密钥 (PSK)
    uint8_t  network_key_length;         // 密钥长度
    uint16_t auth_type;                  // 认证类型 (WPA2-PSK 等)
    uint16_t encr_type;                  // 加密类型 (AES 等)
    uint8_t  mac_address[6];             // AP 的 MAC 地址
    uint8_t  network_key_index;          // 密钥索引

    // EasyMesh 扩展配置
    struct easymesh_config {
        uint8_t  backhaul_ssid[32];      // 回程 SSID
        uint8_t  backhaul_key[64];       // 回程密钥
        uint8_t  device_role;            // 设备角色 (Controller/Agent)
        uint8_t  multi_ap_profile;       // Multi-AP 配置文件版本
    } easymesh;
};
```

#### M8 消息 (Enrollee Confirmation) - 配置确认

**作用**: Enrollee 确认收到配置并验证完整性

```c
// M8 消息详细结构
struct wps_m8_message {
    uint8_t  version;                    // WPS 版本
    uint8_t  message_type;               // 0x0C (M8)
    uint8_t  enrollee_nonce[16];         // 来自 M1 的随机数

    // 加密的 R-S2
    uint8_t  encrypted_r_s2[16];         // 使用 KeyWrapKey 加密的 R-S2

    // 可选的加密设置 (通常为空)
    struct encrypted_settings {
        uint16_t length;                 // 通常为 0
        uint8_t  encrypted_data[];       // 通常为空
    } settings;
};
```

#### WPS 消息交换完整流程图

```
WPS M1-M8 消息交换详细流程:

Enrollee                                    Registrar
   │                                           │
   │ M1: 设备身份介绍                          │
   │ ─────────────────────────────────────────►│
   │ • UUID, MAC, 设备信息                    │ • 验证设备能力
   │ • DH 公钥 (PKE)                          │ • 生成 DH 密钥对
   │ • 支持的认证/加密类型                    │ • 计算共享密钥
   │ • 配置方法 (PBC/PIN)                     │
   │                                           │
   │                          M2: 配置器响应  │
   │◄─────────────────────────────────────────│
   │ • 验证 Registrar 能力    │ • Registrar 信息
   │ • 计算共享密钥           │ • DH 公钥 (PKR)
   │ • 派生认证密钥           │ • 协商的认证/加密类型
   │                                           │
   │ M3: 身份验证开始                          │
   │ ─────────────────────────────────────────►│
   │ • E-Hash1 (PIN 前半部分验证)             │ • 验证 E-Hash1
   │                                           │ • 准备 R-Hash1
   │                                           │
   │                          M4: 配置器验证  │
   │◄─────────────────────────────────────────│
   │ • 验证 R-Hash1           │ • R-Hash1
   │                          │ • 加密的 R-S1
   │                                           │
   │ M5: 设备凭据                              │
   │ ─────────────────────────────────────────►│
   │ • E-Hash2 (PIN 后半部分验证)             │ • 验证 E-Hash2
   │ • 加密的 E-S1                            │ • 解密并验证 E-S1
   │                                           │
   │                          M6: 配置器凭据  │
   │◄─────────────────────────────────────────│
   │ • 验证 R-Hash2           │ • R-Hash2
   │ • 解密并验证 R-S2        │ • 加密的 R-S2
   │                                           │
   │ M7: 网络配置传输 (关键!)                  │
   │◄─────────────────────────────────────────│
   │ • 解密网络配置           │ • 加密的网络设置:
   │ • 获得 SSID/PSK          │   - SSID
   │ • 获得安全参数           │   - Network Key (PSK)
   │ • 应用配置               │   - 认证类型
   │                          │   - 加密类型
   │                          │   - EasyMesh 扩展配置
   │                                           │
   │ M8: 配置确认                              │
   │ ─────────────────────────────────────────►│
   │ • 确认配置接收成功                        │ • 配置完成确认
   │ • 加密的 R-S2 (验证)                     │
   │                                           │
   │ 使用新配置重新连接                        │
   │◄─────────────────────────────────────────►│
```

#### 安全机制详解

```c
// WPS 安全机制的关键组件
struct wps_security_mechanism {
    // 1. DH 密钥交换
    struct dh_key_exchange {
        uint8_t  private_key[192];       // 私钥 (1536位)
        uint8_t  public_key[192];        // 公钥 (1536位)
        uint8_t  shared_secret[192];     // 共享密钥
        /*
         * 计算过程:
         * 1. 生成随机私钥 a (Enrollee) 和 b (Registrar)
         * 2. 计算公钥: PKE = g^a mod p, PKR = g^b mod p
         * 3. 计算共享密钥: SS = PKR^a mod p = PKE^b mod p
         */
    } dh;

    // 2. 密钥派生
    struct key_derivation {
        uint8_t  kdk[32];                // 密钥派生密钥
        uint8_t  auth_key[32];           // 认证密钥
        uint8_t  key_wrap_key[16];       // 密钥包装密钥
        uint8_t  emsk[32];               // 扩展主会话密钥
        /*
         * 派生过程:
         * 1. KDK = HMAC-SHA256(DH_KEY, "Wi-Fi Easy and Secure Key Derivation")
         * 2. AuthKey = KDF(KDK, "WPS Key Derivation", EnrolleeNonce || RegistrarNonce)[0:32]
         * 3. KeyWrapKey = KDF(KDK, "WPS Key Derivation", EnrolleeNonce || RegistrarNonce)[32:48]
         * 4. EMSK = KDF(KDK, "WPS Key Derivation", EnrolleeNonce || RegistrarNonce)[48:80]
         */
    } kdf;

    // 3. PIN 验证机制
    struct pin_verification {
        uint8_t  device_pin[8];          // 8位设备 PIN
        uint8_t  psk1[16];               // PIN 前半部分哈希
        uint8_t  psk2[16];               // PIN 后半部分哈希
        uint8_t  e_s1[16];               // Enrollee Secret 1
        uint8_t  e_s2[16];               // Enrollee Secret 2
        uint8_t  r_s1[16];               // Registrar Secret 1
        uint8_t  r_s2[16];               // Registrar Secret 2
        /*
         * PIN 分割验证:
         * 1. 将 8位 PIN 分为两部分: PIN1 (前4位) + PIN2 (后3位+校验位)
         * 2. PSK1 = SHA256(PIN1), PSK2 = SHA256(PIN2)
         * 3. 分两阶段验证，防止暴力破解
         */
    } pin;

    // 4. 消息完整性保护
    struct message_integrity {
        uint8_t  authenticator[8];       // 消息认证码
        /*
         * 计算: HMAC-SHA256(AuthKey, 消息内容)[0:8]
         */
    } integrity;
};

// PIN 校验和计算
uint32_t calculate_pin_checksum(uint32_t pin) {
    uint32_t accum = 0;

    // 计算前7位数字的校验和
    pin *= 10;  // 去掉校验位
    accum += 3 * ((pin / 10000000) % 10);
    accum += 1 * ((pin / 1000000) % 10);
    accum += 3 * ((pin / 100000) % 10);
    accum += 1 * ((pin / 10000) % 10);
    accum += 3 * ((pin / 1000) % 10);
    accum += 1 * ((pin / 100) % 10);
    accum += 3 * ((pin / 10) % 10);

    return (10 - (accum % 10)) % 10;
}

// 哈希计算示例
void calculate_wps_hashes(struct wps_security *sec) {
    uint8_t hash_input[512];
    int offset = 0;

    // E-Hash1 = HMAC-SHA256(AuthKey, E-S1 || PSK1 || PKE || PKR)
    memcpy(hash_input + offset, sec->e_s1, 16);
    offset += 16;
    memcpy(hash_input + offset, sec->psk1, 16);
    offset += 16;
    memcpy(hash_input + offset, sec->enrollee_public_key, 192);
    offset += 192;
    memcpy(hash_input + offset, sec->registrar_public_key, 192);
    offset += 192;

    hmac_sha256(sec->auth_key, 32, hash_input, offset, sec->e_hash1);

    // R-Hash1 = HMAC-SHA256(AuthKey, R-S1 || PSK1 || PKE || PKR)
    offset = 0;
    memcpy(hash_input + offset, sec->r_s1, 16);
    offset += 16;
    memcpy(hash_input + offset, sec->psk1, 16);
    offset += 16;
    memcpy(hash_input + offset, sec->enrollee_public_key, 192);
    offset += 192;
    memcpy(hash_input + offset, sec->registrar_public_key, 192);
    offset += 192;

    hmac_sha256(sec->auth_key, 32, hash_input, offset, sec->r_hash1);
}
```

#### EasyMesh 中的 WPS 扩展

```c
// EasyMesh 特有的 WPS 配置内容
struct easymesh_wps_extensions {
    // 在 M7 消息中传输的 EasyMesh 特有配置
    struct multi_ap_configuration {
        // 基本网络配置
        uint8_t  fronthaul_ssid[32];     // 前端 SSID
        uint8_t  fronthaul_key[64];      // 前端密钥
        uint8_t  backhaul_ssid[32];      // 回程 SSID
        uint8_t  backhaul_key[64];       // 回程密钥

        // Multi-AP 角色配置
        uint8_t  device_role;            // 0x00=Controller, 0x01=Agent
        uint8_t  multi_ap_profile;       // Multi-AP 配置文件版本

        // 频段配置
        struct band_configuration {
            uint8_t  band_2g_enabled;    // 2.4GHz 频段启用
            uint8_t  band_5g_enabled;    // 5GHz 频段启用
            uint8_t  band_6g_enabled;    // 6GHz 频段启用 (Wi-Fi 6E)
            uint8_t  backhaul_band;      // 专用回程频段
        } bands;

        // 网络策略
        struct network_policies {
            uint8_t  band_steering_enabled;      // 频段引导
            uint8_t  load_balancing_enabled;     // 负载均衡
            uint8_t  fast_roaming_enabled;       // 快速漫游
            uint8_t  airtime_fairness_enabled;   // 空口时间公平性
        } policies;

        // 安全配置
        struct security_configuration {
            uint16_t auth_type_fronthaul;        // 前端认证类型
            uint16_t encr_type_fronthaul;        // 前端加密类型
            uint16_t auth_type_backhaul;         // 回程认证类型
            uint16_t encr_type_backhaul;         // 回程加密类型
            uint8_t  wpa3_transition_mode;       // WPA3 过渡模式
        } security;
    } multi_ap_config;
};
```

#### 消息交换的关键时间点

```c
// WPS 消息交换的时间要求
struct wps_timing_requirements {
    uint32_t message_timeout;            // 单个消息超时: 30秒
    uint32_t total_session_timeout;      // 总会话超时: 120秒
    uint32_t retry_interval;             // 重试间隔: 5秒
    uint32_t max_retries;                // 最大重试次数: 3次

    // 关键时间点
    struct timing_milestones {
        uint32_t m1_sent;                // M1 发送时间
        uint32_t m2_received;            // M2 接收时间
        uint32_t authentication_start;   // 认证开始 (M3)
        uint32_t credential_exchange;    // 凭据交换 (M5-M6)
        uint32_t configuration_received; // 配置接收 (M7)
        uint32_t session_complete;       // 会话完成 (M8)
    } milestones;
};
```

**总结**: WPS M1-M8 消息实现了一个完整的设备配置协议，通过 DH 密钥交换、PIN 验证、分阶段认证等机制确保安全性，最终在 M7 消息中传输实际的网络配置信息。在 EasyMesh 中，这些消息被封装在 IEEE 1905.1 协议中，并扩展了 Multi-AP 特有的配置内容。

## 11. EasyMesh 组网流程详解

### 11.1 组网流程概述

EasyMesh 支持两种主要的组网方式：**无线组网** 和 **有线组网**。两种方式在底层传输上有所不同，但在上层协议和流程上基本一致。

```
EasyMesh 组网方式对比:
┌─────────────────────────────────────────────────────────────┐
│                    无线组网 (Wireless)                      │
├─────────────────────────────────────────────────────────────┤
│ 传输层: 802.11 Wi-Fi                                       │
│ 协议栈: 802.11 → Ethernet → IEEE 1905.1 → WSC/Multi-AP    │
│ 特点: 灵活部署，无需布线，但受信号覆盖限制                 │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    有线组网 (Wired)                         │
├─────────────────────────────────────────────────────────────┤
│ 传输层: Ethernet                                           │
│ 协议栈: Ethernet → IEEE 1905.1 → WSC/Multi-AP             │
│ 特点: 稳定可靠，高带宽，但需要网线布设                     │
└─────────────────────────────────────────────────────────────┘

共同的组网阶段:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Onboarding    │───►│  Configuration  │───►│   Discovery     │
│   (设备加入)     │    │   (配置同步)     │    │   (拓扑发现)     │
│ • 设备发现      │    │ • WPS 配置      │    │ • 邻居发现      │
│ • 身份验证      │    │ • 网络参数      │    │ • 拓扑构建      │
│ • 初始连接      │    │ • 角色分配      │    │ • 路径计算      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │   Operation     │
                    │   (正常运行)     │
                    │ • 客户端管理    │
                    │ • 负载均衡      │
                    │ • 性能优化      │
                    └─────────────────┘
```

## 12. 无线组网流程详解

### 12.1 无线组网协议栈

```
无线组网协议栈结构:
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application)                     │
│                  EasyMesh 管理应用                          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                Multi-AP 协议层 (Multi-AP)                   │
│        客户端管理 | 频段引导 | 负载均衡 | 性能优化          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                WSC 协议层 (Wi-Fi Simple Config)             │
│              WPS M1-M8 消息 | 设备配置 | 安全认证           │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│              IEEE 1905.1 抽象层 (Abstraction Layer)         │
│        拓扑发现 | 消息路由 | 接口抽象 | EtherType: 0x893A    │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  以太网层 (Ethernet)                        │
│              MAC 地址 | 帧格式 | 错误检测                   │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Wi-Fi 层 (802.11)                        │
│        物理层 | MAC 层 | 关联管理 | 安全 (WPA2/WPA3)        │
└─────────────────────────────────────────────────────────────┘
```

### 12.2 无线组网阶段一：Onboarding (设备加入)

#### 12.2.1 无线设备发现

**协议**: 802.11 管理帧 + Multi-AP IE

```
无线设备发现流程:
New Agent                           Existing Controller
    │                                      │
    │ 1.1 Wi-Fi 扫描 (802.11)              │
    │ ────────────────────────────────────►│
    │     协议: 802.11 Probe Request       │
    │     包含: Multi-AP IE (0x65)         │
    │     目的: 发现 EasyMesh 网络         │
    │                                      │
    │◄─── 1.2 信标响应 (802.11) ──────────│
    │     协议: 802.11 Beacon/Probe Resp   │
    │     包含: Multi-AP IE                │
    │     内容: EasyMesh 能力标志          │
    │                                      │
    │ 1.3 关联请求 (802.11)                │
    │ ────────────────────────────────────►│
    │     协议: 802.11 Association Req     │ • 验证设备能力
    │     包含: Multi-AP 扩展              │ • 检查网络容量
    │                                      │ • 分配临时访问
    │                                      │
    │◄─── 1.4 关联响应 (802.11) ──────────│
    │     协议: 802.11 Association Resp    │
    │     状态: 成功/失败                  │
    │                                      │
    │ 1.5 建立基本连接                     │
    │◄───────────────────────────────────►│
    │     协议: 802.11 数据帧              │
    │     状态: 已关联但未配置             │
```

#### 12.2.2 无线身份验证

**协议**: IEEE 1905.1 + 设备认证

```c
// 无线身份验证数据结构
struct wireless_authentication {
    // 802.11 连接信息
    struct wifi_connection_info {
        uint8_t  bssid[6];               // 关联的 BSSID
        uint8_t  ssid[32];               // 临时 SSID
        uint8_t  channel;                // 工作信道
        int8_t   rssi;                   // 信号强度
        uint16_t capability;             // 能力信息
    } wifi_info;

    // 1905.1 设备身份
    struct device_identity_1905 {
        uint8_t  al_mac_address[6];      // 抽象层 MAC
        uint16_t device_type;            // 设备类型
        uint8_t  device_name[64];        // 设备名称
        uint32_t ieee1905_version;       // 1905.1 版本
    } identity_1905;
};
```

```
无线身份验证流程:
New Agent                           Controller
    │                                      │
    │ 2.1 发送设备身份 (IEEE 1905.1)       │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 验证设备身份
    │     EtherType: 0x893A                │ • 检查制造商 OUI
    │     消息类型: Device Information     │ • 验证设备类别
    │     传输: 802.11 数据帧              │ • 应用安全策略
    │                                      │
    │                   2.2 认证挑战       │
    │◄────────────────────────────────────│
    │     协议: IEEE 1905.1                │
    │     消息类型: Authentication Request │
    │     要求: 证书/PIN/按钮确认          │
    │                                      │
    │ 2.3 提供认证凭据                     │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 验证凭据
    │     消息类型: Authentication Response│ • 检查证书链
    │     内容: 设备证书或 WPS PIN         │ • 确认设备合法性
    │                                      │
    │                   2.4 认证结果       │
    │◄────────────────────────────────────│
    │     协议: IEEE 1905.1                │
    │     消息类型: Authentication Result  │
    │     状态: 成功/失败                  │
```

### 12.3 无线组网阶段二：Configuration (配置同步)

#### 12.3.1 无线 WPS 配置

**协议**: WSC (Wi-Fi Simple Configuration) over IEEE 1905.1

```
无线 WPS 配置流程:
New Agent                           Controller
    │                                      │
    │ 3.1 启动 WPS 会话 (IEEE 1905.1)      │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 准备 WPS 配置
    │     EtherType: 0x893A                │ • 生成网络凭据
    │     消息类型: AP Autoconfiguration   │ • 设置安全参数
    │     传输: 802.11 数据帧              │
    │                                      │
    │                   3.2 WPS 响应       │
    │◄────────────────────────────────────│
    │     协议: IEEE 1905.1                │
    │     消息类型: AP Autoconfig Response │
    │     状态: 准备开始 WPS               │
    │                                      │
    │ 3.3 WPS M1-M8 消息交换               │
    │◄───────────────────────────────────►│
    │     协议: WSC over IEEE 1905.1       │
    │     封装: 1905.1 → WSC → WPS 消息    │
    │     传输: 802.11 数据帧              │
    │     内容: 详见前面 M1-M8 解析        │
    │                                      │
    │ 3.4 接收网络配置 (M7)                │
    │◄────────────────────────────────────│
    │     协议: WSC M7 Message             │
    │     内容:                            │
    │     - 前端 SSID/密钥                 │
    │     - 回程 SSID/密钥                 │
    │     - 安全参数 (WPA2/WPA3)           │
    │     - Multi-AP 角色                  │
    │     - 频段配置                       │
    │                                      │
    │ 3.5 确认配置 (M8)                    │
    │ ────────────────────────────────────►│
    │     协议: WSC M8 Message             │
    │     状态: 配置接收成功               │
```

#### 12.3.2 无线配置应用

```c
// 无线配置应用过程
struct wireless_config_application {
    // 接收到的配置
    struct received_wireless_config {
        // 前端配置
        struct fronthaul_config {
            uint8_t  ssid_2g[32];        // 2.4GHz SSID
            uint8_t  ssid_5g[32];        // 5GHz SSID
            uint8_t  ssid_6g[32];        // 6GHz SSID (Wi-Fi 6E)
            uint8_t  passphrase[64];     // 统一密码
            uint16_t auth_type;          // WPA2/WPA3
            uint16_t encr_type;          // AES/TKIP
        } fronthaul;

        // 回程配置
        struct backhaul_config {
            uint8_t  ssid[32];           // 回程 SSID
            uint8_t  passphrase[64];     // 回程密码
            uint8_t  dedicated_band;     // 专用回程频段
            bool     use_fronthaul;      // 是否共享前端
        } backhaul;

        // 无线参数
        struct wireless_parameters {
            uint8_t  channel_2g;         // 2.4GHz 信道
            uint8_t  channel_5g_low;     // 5GHz 低频段
            uint8_t  channel_5g_high;    // 5GHz 高频段
            uint8_t  channel_6g;         // 6GHz 信道
            int8_t   tx_power_2g;        // 2.4GHz 发射功率
            int8_t   tx_power_5g;        // 5GHz 发射功率
            int8_t   tx_power_6g;        // 6GHz 发射功率
        } wireless_params;
    } config;

    // 配置应用状态
    enum wireless_config_state {
        WIRELESS_CONFIG_RECEIVED,        // 配置已接收
        WIRELESS_CONFIG_VALIDATING,      // 验证配置
        WIRELESS_CONFIG_APPLYING,        // 应用配置
        WIRELESS_CONFIG_RESTARTING,      // 重启接口
        WIRELESS_CONFIG_COMPLETE         // 配置完成
    } state;
};
```

```
无线配置应用流程:
New Agent                           Controller
    │                                      │
    │ 4.1 验证无线配置                     │
    │     检查:                            │
    │     - SSID 有效性                    │
    │     - 密码强度                       │
    │     - 信道合法性                     │
    │     - 功率范围                       │
    │                                      │
    │ 4.2 应用无线配置                     │
    │     配置:                            │
    │     - 创建前端 VAP                   │
    │     - 创建回程 VAP                   │
    │     - 设置安全参数                   │
    │     - 配置信道和功率                 │
    │                                      │
    │ 4.3 重启无线接口                     │
    │     重新启动 802.11 接口             │
    │                                      │
    │ 4.4 验证配置生效                     │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 验证设备配置
    │     消息类型: Configuration Status   │ • 检查无线连接
    │     传输: 新的 802.11 连接           │ • 确认角色分配
    │                                      │
    │                   4.5 配置确认       │
    │◄────────────────────────────────────│
    │     协议: IEEE 1905.1                │
    │     状态: 配置成功                   │
```

### 12.4 无线组网阶段三：Discovery (拓扑发现)

#### 12.4.1 无线邻居发现

**协议**: IEEE 1905.1 Topology Discovery over 802.11

```
无线邻居发现流程:
New Agent                           Network Devices
    │                                      │
    │ 5.1 发送拓扑发现 (IEEE 1905.1)       │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 所有设备接收
    │     EtherType: 0x893A                │ • 更新邻居表
    │     消息类型: Topology Discovery     │ • 记录链路信息
    │     目标: 组播 01:80:C2:00:00:13     │
    │     传输: 802.11 组播数据帧          │
    │                                      │
    │◄─── 5.2 拓扑响应 (IEEE 1905.1) ─────│
    │     协议: IEEE 1905.1                │
    │     消息类型: Topology Response      │
    │     内容:                            │
    │     - 设备 AL MAC                    │
    │     - 无线接口信息                   │
    │     - 设备能力                       │
    │     - 无线链路度量                   │
    │     传输: 802.11 单播数据帧          │
    │                                      │
    │ 5.3 构建无线邻居表                   │
    │     记录:                            │
    │     - 邻居设备列表                   │
    │     - RSSI 和信号质量                │
    │     - 支持的频段                     │
    │     - 链路容量                       │
```

#### 12.4.2 无线拓扑构建

**协议**: IEEE 1905.1 Topology Query/Response

```c
// 无线拓扑信息
struct wireless_topology_info {
    // 无线链路信息
    struct wireless_link {
        uint8_t  neighbor_al_mac[6];     // 邻居 AL MAC
        uint8_t  local_interface_mac[6]; // 本地接口 MAC
        uint8_t  neighbor_interface_mac[6]; // 邻居接口 MAC

        // 无线特有信息
        struct wireless_link_info {
            uint8_t  band;               // 频段 (2.4G/5G/6G)
            uint8_t  channel;            // 信道
            int8_t   rssi;               // 信号强度
            uint16_t phy_rate;           // 物理速率
            uint8_t  bandwidth;          // 带宽 (20/40/80/160MHz)
            bool     is_backhaul;        // 是否为回程链路
        } wireless_info;

        // 链路质量度量
        struct wireless_link_metrics {
            uint32_t tx_packets;         // 发送包数
            uint32_t rx_packets;         // 接收包数
            uint32_t tx_packet_errors;   // 发送错误
            uint32_t rx_packet_errors;   // 接收错误
            uint16_t link_availability;  // 链路可用性
            uint16_t mac_throughput_capacity; // MAC 吞吐量容量
        } metrics;
    } wireless_links[MAX_WIRELESS_LINKS];
};
```

```
无线拓扑构建流程:
New Agent                           Controller
    │                                      │
    │ 6.1 发送拓扑查询 (IEEE 1905.1)       │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 查询全网拓扑
    │     消息类型: Topology Query         │ • 收集设备信息
    │     传输: 802.11 数据帧              │ • 分析无线链路
    │                                      │
    │                   6.2 拓扑响应       │
    │◄────────────────────────────────────│
    │     协议: IEEE 1905.1                │
    │     消息类型: Topology Response      │
    │     内容:                            │
    │     - 完整网络拓扑                   │
    │     - 所有无线链路                   │
    │     - 设备间连接关系                 │
    │     - 无线链路质量                   │
    │     传输: 802.11 数据帧              │
    │                                      │
    │ 6.3 构建无线拓扑数据库               │
    │     建立:                            │
    │     - 无线邻接矩阵                   │
    │     - 无线设备能力表                 │
    │     - 无线链路度量表                 │
    │     - 信道使用情况                   │
```

### 12.5 无线组网阶段四：Operation (正常运行)

#### 12.5.1 无线客户端管理

**协议**: Multi-AP + 802.11k/v/r

```
无线客户端管理流程:
Controller                          Agents
    │                                  │
    │ 7.1 客户端状态监控               │
    │◄─────────────────────────────────│
    │     协议: Multi-AP               │
    │     消息类型: Client Capability  │
    │     传输: IEEE 1905.1 over 802.11│
    │     内容:                        │
    │     - 客户端关联状态             │
    │     - RSSI 测量                  │
    │     - 802.11k/v/r 能力           │
    │     - 流量统计                   │
    │                                  │
    │ 7.2 无线引导决策                 │
    │     分析:                        │
    │     - 频段负载分布               │
    │     - 信号强度分布               │
    │     - 客户端能力                 │
    │     - 信道干扰情况               │
    │                                  │
    │ 7.3 发送引导指令                 │
    │ ─────────────────────────────────►│
    │     协议: Multi-AP               │ • 执行频段引导
    │     消息类型: Client Steering    │ • 发送 BTM 请求
    │     传输: IEEE 1905.1 over 802.11│ • 调整信号强度
    │     内容:                        │ • 阻塞关联请求
    │     - 目标客户端 MAC             │
    │     - 目标频段/AP                │
    │     - 引导方法 (BTM/信号调整)    │
    │                                  │
    │◄─── 7.4 引导结果报告 ───────────│
    │     协议: Multi-AP               │
    │     消息类型: BTM Report         │
    │     传输: IEEE 1905.1 over 802.11│
    │     状态: 引导成功/失败          │
```

#### 12.5.2 无线性能优化

**协议**: Multi-AP + IEEE 1905.1

```c
// 无线性能优化
struct wireless_performance_optimization {
    // 信道优化
    struct wireless_channel_optimization {
        // 信道扫描结果
        struct channel_scan_result {
            uint8_t  channel;            // 信道号
            uint8_t  band;               // 频段
            int8_t   noise_floor;        // 噪声底
            float    utilization;        // 信道利用率
            uint32_t overlapping_bss;    // 重叠 BSS 数量
            int8_t   max_rssi;           // 最强干扰信号
        } scan_results[MAX_CHANNELS];

        // 优化算法
        uint8_t select_optimal_wireless_channel(
            struct channel_scan_result *results,
            int count, uint8_t band) {

            uint8_t best_channel = 0;
            float best_score = 0;

            for (int i = 0; i < count; i++) {
                if (results[i].band != band) continue;

                float score = 100.0;
                score -= results[i].utilization * 50;  // 利用率权重
                score -= results[i].overlapping_bss * 10; // 干扰权重
                score -= (results[i].noise_floor + 100) * 2; // 噪声权重

                if (score > best_score) {
                    best_score = score;
                    best_channel = results[i].channel;
                }
            }

            return best_channel;
        }
    } channel_opt;

    // 功率优化
    struct wireless_power_optimization {
        int8_t calculate_optimal_wireless_power(
            struct wireless_ap_info *ap) {

            int8_t optimal_power = ap->current_tx_power;

            // 基于客户端 RSSI 调整
            if (ap->min_client_rssi < -75) {
                optimal_power = min(ap->max_tx_power, optimal_power + 3);
            } else if (ap->min_client_rssi > -45) {
                optimal_power = max(ap->min_tx_power, optimal_power - 2);
            }

            // 基于邻居干扰调整
            if (ap->neighbor_interference > HIGH_INTERFERENCE) {
                optimal_power = max(ap->min_tx_power, optimal_power - 1);
            }

            return optimal_power;
        }
    } power_opt;
};
```

```
无线性能优化流程:
Controller                          Network
    │                                  │
    │ 8.1 无线性能监控                 │
    │◄─────────────────────────────────│
    │     协议: Multi-AP               │
    │     消息类型: Channel Scan Result│
    │     传输: IEEE 1905.1 over 802.11│
    │     内容:                        │
    │     - 信道扫描结果               │
    │     - 干扰水平                   │
    │     - 客户端 RSSI 分布           │
    │     - 吞吐量统计                 │
    │                                  │
    │ 8.2 无线优化分析                 │
    │     识别问题:                    │
    │     - 信道拥塞                   │
    │     - 功率不当                   │
    │     - 频段负载不均               │
    │     - 干扰过强                   │
    │                                  │
    │ 8.3 无线优化决策                 │
    │     制定策略:                    │
    │     - 信道切换                   │
    │     - 功率调整                   │
    │     - 频段重分配                 │
    │     - 客户端重引导               │
    │                                  │
    │ 8.4 执行无线优化                 │
    │ ─────────────────────────────────►│
    │     协议: Multi-AP               │ • 应用新配置
    │     消息类型: Channel Preference │ • 切换信道
    │     传输: IEEE 1905.1 over 802.11│ • 调整功率
    │     内容: 优化指令               │ • 监控效果
    │                                  │
    │◄─── 8.5 优化结果 ──────────────│
    │     协议: Multi-AP               │
    │     消息类型: Operating Channel  │
    │     状态: 优化完成               │
```

## 13. 有线组网流程详解

### 13.1 有线组网协议栈

```
有线组网协议栈结构:
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application)                     │
│                  EasyMesh 管理应用                          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                Multi-AP 协议层 (Multi-AP)                   │
│        客户端管理 | 频段引导 | 负载均衡 | 性能优化          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                WSC 协议层 (Wi-Fi Simple Config)             │
│              WPS M1-M8 消息 | 设备配置 | 安全认证           │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│              IEEE 1905.1 抽象层 (Abstraction Layer)         │
│        拓扑发现 | 消息路由 | 接口抽象 | EtherType: 0x893A    │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  以太网层 (Ethernet)                        │
│        MAC 地址 | 帧格式 | 错误检测 | 链路聚合              │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   物理层 (Physical)                         │
│        10/100/1000 Mbps | 2.5G/5G/10G | 光纤/双绞线        │
└─────────────────────────────────────────────────────────────┘

关键差异:
• 无需 802.11 层，直接使用以太网
• 更稳定的物理连接
• 更高的带宽和更低的延迟
• 支持链路聚合和冗余
```

### 13.2 有线组网阶段一：Onboarding (设备加入)

#### 13.2.1 有线设备发现

**协议**: IEEE 1905.1 over Ethernet

```
有线设备发现流程:
New Agent                           Existing Controller
    │                                      │
    │ 1.1 以太网链路建立                   │
    │◄───────────────────────────────────►│
    │     协议: Ethernet                   │
    │     状态: 物理链路 UP                │
    │     速度: 自动协商 (10M-10G)         │
    │                                      │
    │ 1.2 发送设备发现 (IEEE 1905.1)       │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 检测新设备
    │     EtherType: 0x893A                │ • 验证设备能力
    │     消息类型: Topology Discovery     │ • 检查网络容量
    │     目标: 组播 01:80:C2:00:00:13     │ • 分配临时访问
    │     传输: Ethernet 帧                │
    │                                      │
    │◄─── 1.3 发现响应 (IEEE 1905.1) ─────│
    │     协议: IEEE 1905.1                │
    │     消息类型: Topology Response      │
    │     内容: EasyMesh 网络信息          │
    │     传输: Ethernet 单播帧            │
    │                                      │
    │ 1.4 建立基本连接                     │
    │◄───────────────────────────────────►│
    │     协议: IEEE 1905.1 over Ethernet  │
    │     状态: 已连接但未配置             │
```

#### 13.2.2 有线身份验证

**协议**: IEEE 1905.1 + 设备认证

```c
// 有线身份验证数据结构
struct wired_authentication {
    // 以太网连接信息
    struct ethernet_connection_info {
        uint8_t  local_mac[6];           // 本地 MAC 地址
        uint8_t  remote_mac[6];          // 对端 MAC 地址
        uint32_t link_speed;             // 链路速度 (Mbps)
        bool     full_duplex;            // 全双工模式
        bool     auto_negotiation;       // 自动协商
        uint16_t mtu;                    // 最大传输单元
    } ethernet_info;

    // 1905.1 设备身份
    struct device_identity_1905 {
        uint8_t  al_mac_address[6];      // 抽象层 MAC
        uint16_t device_type;            // 设备类型
        uint8_t  device_name[64];        // 设备名称
        uint32_t ieee1905_version;       // 1905.1 版本

        // 有线接口能力
        struct wired_interface_capability {
            uint32_t max_speed;          // 最大速度
            bool     supports_aggregation; // 支持链路聚合
            bool     supports_vlan;      // 支持 VLAN
            bool     supports_qos;       // 支持 QoS
        } wired_capability;
    } identity_1905;
};
```

```
有线身份验证流程:
New Agent                           Controller
    │                                      │
    │ 2.1 发送设备身份 (IEEE 1905.1)       │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 验证设备身份
    │     EtherType: 0x893A                │ • 检查制造商 OUI
    │     消息类型: Device Information     │ • 验证设备类别
    │     传输: Ethernet 帧                │ • 应用安全策略
    │     内容:                            │
    │     - AL MAC 地址                    │
    │     - 设备类型和能力                 │
    │     - 有线接口信息                   │
    │                                      │
    │                   2.2 认证挑战       │
    │◄────────────────────────────────────│
    │     协议: IEEE 1905.1                │
    │     消息类型: Authentication Request │
    │     要求: 证书/PIN/按钮确认          │
    │     传输: Ethernet 帧                │
    │                                      │
    │ 2.3 提供认证凭据                     │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 验证凭据
    │     消息类型: Authentication Response│ • 检查证书链
    │     内容: 设备证书或 WPS PIN         │ • 确认设备合法性
    │     传输: Ethernet 帧                │
    │                                      │
    │                   2.4 认证结果       │
    │◄────────────────────────────────────│
    │     协议: IEEE 1905.1                │
    │     消息类型: Authentication Result  │
    │     状态: 成功/失败                  │
    │     传输: Ethernet 帧                │
```

### 13.3 有线组网阶段二：Configuration (配置同步)

#### 13.3.1 有线 WPS 配置

**协议**: WSC (Wi-Fi Simple Configuration) over IEEE 1905.1 over Ethernet

```
有线 WPS 配置流程:
New Agent                           Controller
    │                                      │
    │ 3.1 启动 WPS 会话 (IEEE 1905.1)      │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 准备 WPS 配置
    │     EtherType: 0x893A                │ • 生成网络凭据
    │     消息类型: AP Autoconfiguration   │ • 设置安全参数
    │     传输: Ethernet 帧                │
    │                                      │
    │                   3.2 WPS 响应       │
    │◄────────────────────────────────────│
    │     协议: IEEE 1905.1                │
    │     消息类型: AP Autoconfig Response │
    │     状态: 准备开始 WPS               │
    │     传输: Ethernet 帧                │
    │                                      │
    │ 3.3 WPS M1-M8 消息交换               │
    │◄───────────────────────────────────►│
    │     协议: WSC over IEEE 1905.1       │
    │     封装: Ethernet → 1905.1 → WSC    │
    │     传输: Ethernet 帧                │
    │     内容: 详见前面 M1-M8 解析        │
    │     注意: 相同的 WPS 消息内容        │
    │           但通过有线传输             │
    │                                      │
    │ 3.4 接收网络配置 (M7)                │
    │◄────────────────────────────────────│
    │     协议: WSC M7 Message             │
    │     内容:                            │
    │     - 前端 SSID/密钥                 │
    │     - 回程 SSID/密钥                 │
    │     - 安全参数 (WPA2/WPA3)           │
    │     - Multi-AP 角色                  │
    │     - 有线接口配置                   │
    │     传输: Ethernet 帧                │
    │                                      │
    │ 3.5 确认配置 (M8)                    │
    │ ────────────────────────────────────►│
    │     协议: WSC M8 Message             │
    │     状态: 配置接收成功               │
    │     传输: Ethernet 帧                │
```

#### 13.3.2 有线配置应用

```c
// 有线配置应用过程
struct wired_config_application {
    // 接收到的有线配置
    struct received_wired_config {
        // 网络配置 (与无线相同)
        struct network_config {
            uint8_t  fronthaul_ssid[32]; // 前端 SSID
            uint8_t  fronthaul_passphrase[64]; // 前端密码
            uint8_t  backhaul_ssid[32];  // 回程 SSID
            uint8_t  backhaul_passphrase[64]; // 回程密码
            uint16_t auth_type;          // 认证类型
            uint16_t encr_type;          // 加密类型
        } network;

        // 有线特有配置
        struct wired_specific_config {
            bool     use_wired_backhaul; // 使用有线回程
            uint16_t vlan_id;            // VLAN ID
            uint8_t  qos_priority;       // QoS 优先级
            bool     enable_aggregation; // 启用链路聚合
            uint32_t bandwidth_limit;    // 带宽限制
        } wired_config;

        // 设备角色
        uint8_t  device_role;            // Controller/Agent
        uint8_t  multi_ap_profile;       // Multi-AP 版本
    } config;

    // 配置应用状态
    enum wired_config_state {
        WIRED_CONFIG_RECEIVED,           // 配置已接收
        WIRED_CONFIG_VALIDATING,         // 验证配置
        WIRED_CONFIG_APPLYING,           // 应用配置
        WIRED_CONFIG_COMPLETE            // 配置完成
    } state;
};
```

```
有线配置应用流程:
New Agent                           Controller
    │                                      │
    │ 4.1 验证有线配置                     │
    │     检查:                            │
    │     - 网络参数有效性                 │
    │     - VLAN 配置                      │
    │     - QoS 设置                       │
    │     - 带宽限制                       │
    │                                      │
    │ 4.2 应用有线配置                     │
    │     配置:                            │
    │     - 创建无线 VAP                   │
    │     - 配置有线接口                   │
    │     - 设置 VLAN                      │
    │     - 配置 QoS 策略                  │
    │     - 启用链路聚合 (如需要)          │
    │                                      │
    │ 4.3 验证配置生效                     │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 验证设备配置
    │     消息类型: Configuration Status   │ • 检查有线连接
    │     传输: Ethernet 帧                │ • 确认角色分配
    │     内容:                            │
    │     - 配置应用状态                   │
    │     - 接口状态                       │
    │     - 链路质量                       │
    │                                      │
    │                   4.4 配置确认       │
    │◄────────────────────────────────────│
    │     协议: IEEE 1905.1                │
    │     状态: 配置成功                   │
    │     传输: Ethernet 帧                │
```

### 13.4 有线组网阶段三：Discovery (拓扑发现)

#### 13.4.1 有线邻居发现

**协议**: IEEE 1905.1 Topology Discovery over Ethernet

```
有线邻居发现流程:
New Agent                           Network Devices
    │                                      │
    │ 5.1 发送拓扑发现 (IEEE 1905.1)       │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 所有设备接收
    │     EtherType: 0x893A                │ • 更新邻居表
    │     消息类型: Topology Discovery     │ • 记录链路信息
    │     目标: 组播 01:80:C2:00:00:13     │
    │     传输: Ethernet 组播帧            │
    │                                      │
    │◄─── 5.2 拓扑响应 (IEEE 1905.1) ─────│
    │     协议: IEEE 1905.1                │
    │     消息类型: Topology Response      │
    │     内容:                            │
    │     - 设备 AL MAC                    │
    │     - 有线接口信息                   │
    │     - 设备能力                       │
    │     - 有线链路度量                   │
    │     传输: Ethernet 单播帧            │
    │                                      │
    │ 5.3 构建有线邻居表                   │
    │     记录:                            │
    │     - 邻居设备列表                   │
    │     - 链路速度和质量                 │
    │     - 支持的功能                     │
    │     - 带宽容量                       │
```

#### 13.4.2 有线拓扑构建

**协议**: IEEE 1905.1 Topology Query/Response over Ethernet

```c
// 有线拓扑信息
struct wired_topology_info {
    // 有线链路信息
    struct wired_link {
        uint8_t  neighbor_al_mac[6];     // 邻居 AL MAC
        uint8_t  local_interface_mac[6]; // 本地接口 MAC
        uint8_t  neighbor_interface_mac[6]; // 邻居接口 MAC

        // 有线特有信息
        struct wired_link_info {
            uint32_t link_speed;         // 链路速度 (Mbps)
            bool     full_duplex;        // 全双工
            uint16_t mtu;                // MTU 大小
            bool     aggregated;         // 是否聚合链路
            uint16_t vlan_id;            // VLAN ID
            uint8_t  qos_priority;       // QoS 优先级
        } wired_info;

        // 有线链路质量度量
        struct wired_link_metrics {
            uint64_t tx_bytes;           // 发送字节数
            uint64_t rx_bytes;           // 接收字节数
            uint32_t tx_packets;         // 发送包数
            uint32_t rx_packets;         // 接收包数
            uint32_t tx_errors;          // 发送错误
            uint32_t rx_errors;          // 接收错误
            uint32_t collisions;         // 冲突数
            uint16_t link_availability;  // 链路可用性 (%)
            uint32_t throughput_capacity; // 吞吐量容量
        } metrics;
    } wired_links[MAX_WIRED_LINKS];

    // 交换机信息 (如果存在)
    struct switch_info {
        uint8_t  switch_mac[6];          // 交换机 MAC
        uint16_t vlan_support;           // VLAN 支持
        bool     qos_support;            // QoS 支持
        uint32_t port_count;             // 端口数量
        uint32_t forwarding_table_size;  // 转发表大小
    } switches[MAX_SWITCHES];
};
```

```
有线拓扑构建流程:
New Agent                           Controller
    │                                      │
    │ 6.1 发送拓扑查询 (IEEE 1905.1)       │
    │ ────────────────────────────────────►│
    │     协议: IEEE 1905.1                │ • 查询全网拓扑
    │     消息类型: Topology Query         │ • 收集设备信息
    │     传输: Ethernet 帧                │ • 分析有线链路
    │                                      │ • 检测交换机
    │                                      │
    │                   6.2 拓扑响应       │
    │◄────────────────────────────────────│
    │     协议: IEEE 1905.1                │
    │     消息类型: Topology Response      │
    │     内容:                            │
    │     - 完整网络拓扑                   │
    │     - 所有有线链路                   │
    │     - 设备间连接关系                 │
    │     - 有线链路质量                   │
    │     - 交换机信息                     │
    │     传输: Ethernet 帧                │
    │                                      │
    │ 6.3 构建有线拓扑数据库               │
    │     建立:                            │
    │     - 有线邻接矩阵                   │
    │     - 有线设备能力表                 │
    │     - 有线链路度量表                 │
    │     - 交换机转发表                   │
```

### 13.5 有线组网阶段四：Operation (正常运行)

#### 13.5.1 有线网络管理

**协议**: Multi-AP + IEEE 1905.1 over Ethernet

```
有线网络管理流程:
Controller                          Agents
    │                                  │
    │ 7.1 有线链路监控                 │
    │◄─────────────────────────────────│
    │     协议: IEEE 1905.1            │
    │     消息类型: Link Metric Query  │
    │     传输: Ethernet 帧            │
    │     内容:                        │
    │     - 链路状态                   │
    │     - 带宽利用率                 │
    │     - 错误统计                   │
    │     - QoS 状态                   │
    │                                  │
    │ 7.2 有线负载分析                 │
    │     分析:                        │
    │     - 链路负载分布               │
    │     - 带宽瓶颈                   │
    │     - 错误率趋势                 │
    │     - QoS 效果                   │
    │                                  │
    │ 7.3 发送优化指令                 │
    │ ─────────────────────────────────►│
    │     协议: Multi-AP               │ • 执行 QoS 调整
    │     消息类型: Policy Config      │ • 调整 VLAN 配置
    │     传输: IEEE 1905.1 over Ethernet│ • 优化路由路径
    │     内容:                        │ • 启用链路聚合
    │     - QoS 策略                   │
    │     - VLAN 配置                  │
    │     - 路由优化                   │
    │                                  │
    │◄─── 7.4 优化结果报告 ───────────│
    │     协议: IEEE 1905.1            │
    │     消息类型: Policy Response    │
    │     传输: Ethernet 帧            │
    │     状态: 优化完成               │
```

#### 13.5.2 有线性能优化

**协议**: Multi-AP + IEEE 1905.1

```c
// 有线性能优化
struct wired_performance_optimization {
    // QoS 优化
    struct wired_qos_optimization {
        // 流量分类
        struct traffic_classification {
            uint8_t  dscp_marking;       // DSCP 标记
            uint16_t vlan_priority;      // VLAN 优先级
            uint32_t bandwidth_limit;    // 带宽限制
            uint32_t burst_size;         // 突发大小
        } traffic_classes[MAX_TRAFFIC_CLASSES];

        // QoS 策略
        struct qos_policy {
            uint8_t  priority_queue[8];  // 优先级队列
            uint32_t queue_weight[8];    // 队列权重
            bool     strict_priority;    // 严格优先级
            bool     weighted_fair_queue; // 加权公平队列
        } qos_policy;
    } qos_opt;

    // 链路聚合优化
    struct link_aggregation_optimization {
        // 聚合配置
        struct aggregation_config {
            uint8_t  member_ports[MAX_PORTS]; // 成员端口
            uint8_t  aggregation_mode;       // 聚合模式 (LACP/Static)
            uint8_t  load_balance_method;    // 负载均衡方法
            uint32_t min_links;              // 最小链路数
        } agg_config;

        // 负载均衡算法
        uint8_t select_egress_port(struct aggregation_config *agg,
                                  struct packet_info *packet) {
            uint32_t hash = 0;

            // 基于源/目标 MAC 的哈希
            hash ^= packet->src_mac[4] << 8 | packet->src_mac[5];
            hash ^= packet->dst_mac[4] << 8 | packet->dst_mac[5];

            // 基于 VLAN ID 的哈希
            hash ^= packet->vlan_id;

            return agg->member_ports[hash % agg->min_links];
        }
    } aggregation_opt;

    // 路径优化
    struct path_optimization {
        uint32_t calculate_wired_path_cost(struct wired_link *link) {
            uint32_t cost = 0;

            // 基于带宽的代价
            cost += 1000000 / link->wired_info.link_speed;

            // 基于错误率的代价
            float error_rate = (float)(link->metrics.tx_errors +
                                     link->metrics.rx_errors) /
                              (link->metrics.tx_packets +
                               link->metrics.rx_packets);
            cost += (uint32_t)(error_rate * 10000);

            // 基于利用率的代价
            float utilization = (float)(link->metrics.tx_bytes +
                                       link->metrics.rx_bytes) /
                               (link->metrics.throughput_capacity * 8);
            if (utilization > 0.8) {
                cost += (uint32_t)((utilization - 0.8) * 5000);
            }

            return cost;
        }
    } path_opt;
};
```

```
有线性能优化流程:
Controller                          Network
    │                                  │
    │ 8.1 有线性能监控                 │
    │◄─────────────────────────────────│
    │     协议: IEEE 1905.1            │
    │     消息类型: Link Metric Report │
    │     传输: Ethernet 帧            │
    │     内容:                        │
    │     - 带宽利用率                 │
    │     - 延迟测量                   │
    │     - 错误统计                   │
    │     - QoS 效果                   │
    │                                  │
    │ 8.2 有线优化分析                 │
    │     识别问题:                    │
    │     - 带宽瓶颈                   │
    │     - QoS 不当                   │
    │     - 路径次优                   │
    │     - 链路故障                   │
    │                                  │
    │ 8.3 有线优化决策                 │
    │     制定策略:                    │
    │     - QoS 调整                   │
    │     - 路径重选                   │
    │     - 链路聚合                   │
    │     - VLAN 优化                  │
    │                                  │
    │ 8.4 执行有线优化                 │
    │ ─────────────────────────────────►│
    │     协议: Multi-AP               │ • 应用新配置
    │     消息类型: Policy Update      │ • 调整 QoS
    │     传输: IEEE 1905.1 over Ethernet│ • 重配置路由
    │     内容: 优化指令               │ • 监控效果
    │                                  │
    │◄─── 8.5 优化结果 ──────────────│
    │     协议: IEEE 1905.1            │
    │     消息类型: Policy Ack         │
    │     状态: 优化完成               │
```

## 14. 无线与有线组网对比总结

### 14.1 协议栈对比

```
协议层对比:
┌─────────────────┬─────────────────┬─────────────────┐
│     协议层      │    无线组网     │    有线组网     │
├─────────────────┼─────────────────┼─────────────────┤
│   应用层        │   EasyMesh 管理应用 (相同)      │
├─────────────────┼─────────────────┼─────────────────┤
│  Multi-AP 层    │   客户端管理、引导、优化 (相同) │
├─────────────────┼─────────────────┼─────────────────┤
│   WSC 层        │   WPS M1-M8 消息 (相同)        │
├─────────────────┼─────────────────┼─────────────────┤
│ IEEE 1905.1 层  │   拓扑发现、消息路由 (相同)     │
├─────────────────┼─────────────────┼─────────────────┤
│   以太网层      │   MAC 地址、帧格式 (相同)       │
├─────────────────┼─────────────────┼─────────────────┤
│   物理层        │   802.11 Wi-Fi  │   Ethernet      │
└─────────────────┴─────────────────┴─────────────────┘
```

### 14.2 关键差异

```
特性对比:
┌─────────────────┬─────────────────┬─────────────────┐
│     特性        │    无线组网     │    有线组网     │
├─────────────────┼─────────────────┼─────────────────┤
│   部署灵活性    │      高         │      低         │
├─────────────────┼─────────────────┼─────────────────┤
│   连接稳定性    │      中         │      高         │
├─────────────────┼─────────────────┼─────────────────┤
│   带宽容量      │   受信号限制    │   高带宽        │
├─────────────────┼─────────────────┼─────────────────┤
│   延迟          │   较高          │   较低          │
├─────────────────┼─────────────────┼─────────────────┤
│   干扰影响      │   受环境影响    │   基本无干扰    │
├─────────────────┼─────────────────┼─────────────────┤
│   安装成本      │   低            │   高 (需布线)   │
├─────────────────┼─────────────────┼─────────────────┤
│   维护复杂度    │   中            │   低            │
└─────────────────┴─────────────────┴─────────────────┘
```

**总结**: 无线和有线组网在上层协议 (Multi-AP、WSC、IEEE 1905.1) 上完全一致，主要差异在物理层传输。无线组网更灵活但受信号限制，有线组网更稳定但需要布线。实际部署中常采用混合模式，骨干网络使用有线连接，边缘设备使用无线扩展。

#### 11.2.1 设备发现阶段

**目标**: 新设备加入网络并被现有网络发现

```c
// Onboarding 阶段的状态机
enum onboarding_state {
    ONBOARDING_IDLE,                     // 空闲状态
    ONBOARDING_SCANNING,                 // 扫描网络
    ONBOARDING_DISCOVERED,               // 被发现
    ONBOARDING_AUTHENTICATING,           // 身份验证
    ONBOARDING_CONNECTED,                // 初始连接
    ONBOARDING_COMPLETE                  // 加入完成
};

// 新设备的发现过程
struct device_onboarding_process {
    // 1. 新设备启动扫描
    struct network_scanning {
        uint32_t scan_duration;          // 扫描持续时间
        uint8_t  scan_channels[];        // 扫描信道列表
        bool     active_scan;            // 主动/被动扫描

        // 扫描结果
        struct discovered_network {
            uint8_t  bssid[6];           // AP BSSID
            uint8_t  ssid[32];           // 网络名称
            int8_t   rssi;               // 信号强度
            uint8_t  channel;            // 信道
            bool     easymesh_capable;   // 是否支持 EasyMesh
            uint8_t  multi_ap_ie[];      // Multi-AP 信息元素
        } networks[MAX_NETWORKS];
    } scanning;

    // 2. 现有网络的发现响应
    struct network_discovery_response {
        bool     accept_new_device;      // 是否接受新设备
        uint8_t  controller_mac[6];      // Controller MAC
        uint32_t network_capacity;       // 网络容量
        uint8_t  security_policy;        // 安全策略
        uint32_t onboarding_timeout;     // 加入超时时间
    } discovery_response;
};
```

**详细执行步骤**:

```
步骤 1: 新设备网络扫描
New Agent                           Existing Network
    │                                      │
    │ 1.1 启动 Wi-Fi 扫描                  │
    │ ────────────────────────────────────►│
    │     扫描所有信道                     │
    │     寻找 EasyMesh 网络               │
    │                                      │
    │ 1.2 检测 Multi-AP IE                 │
    │ ────────────────────────────────────►│
    │     在信标帧中查找:                  │
    │     - Multi-AP IE (0x65)             │
    │     - EasyMesh 能力标志               │
    │                                      │
    │ 1.3 发送探测请求                     │
    │ ────────────────────────────────────►│
    │     包含 Multi-AP 扩展               │
    │                                      │
    │◄─── 1.4 探测响应 ───────────────────│
    │     包含网络信息和加入策略           │

步骤 2: 初始连接建立
New Agent                           Controller
    │                                      │
    │ 2.1 选择最佳网络                     │
    │     基于 RSSI 和网络容量             │
    │                                      │
    │ 2.2 发起关联请求                     │
    │ ────────────────────────────────────►│
    │     使用临时凭据或开放认证           │
    │                                      │
    │◄─── 2.3 关联响应 ───────────────────│
    │     分配临时网络访问权限             │
    │                                      │
    │ 2.4 建立基本连接                     │
    │◄───────────────────────────────────►│
    │     获得有限的网络访问               │
```

#### 11.2.2 身份验证阶段

```c
// 设备身份验证过程
struct device_authentication {
    // 设备身份信息
    struct device_identity {
        uint8_t  device_uuid[16];        // 设备唯一标识
        uint8_t  manufacturer_oui[3];    // 制造商 OUI
        uint8_t  device_category[2];     // 设备类别
        uint8_t  serial_number[32];      // 序列号
        uint8_t  model_name[32];         // 型号名称
        uint32_t firmware_version;       // 固件版本
    } identity;

    // 认证凭据
    struct authentication_credentials {
        uint8_t  device_certificate[512]; // 设备证书
        uint8_t  private_key[256];       // 私钥
        uint8_t  ca_certificate[512];    // CA 证书
        uint8_t  device_pin[8];          // WPS PIN (可选)
    } credentials;

    // 认证策略
    struct auth_policy {
        bool     require_certificate;    // 是否需要证书
        bool     allow_wps_pin;          // 是否允许 WPS PIN
        bool     allow_push_button;      // 是否允许按钮配置
        uint32_t auth_timeout;           // 认证超时
    } policy;
};
```

**身份验证执行流程**:

```
步骤 3: 设备身份验证
New Agent                           Controller
    │                                      │
    │ 3.1 发送设备身份信息                 │
    │ ────────────────────────────────────►│
    │     包含:                            │ • 验证设备身份
    │     - 设备 UUID                      │ • 检查制造商 OUI
    │     - 制造商信息                     │ • 验证设备类别
    │     - 型号和序列号                   │ • 检查安全策略
    │                                      │
    │                   3.2 身份验证请求   │
    │◄────────────────────────────────────│
    │ • 选择认证方法       │ 要求提供:
    │ • 准备认证凭据       │ - 设备证书 或
    │                      │ - WPS PIN 或
    │                      │ - 按钮确认
    │                                      │
    │ 3.3 提供认证凭据                     │
    │ ────────────────────────────────────►│
    │     根据要求提供相应凭据             │ • 验证凭据有效性
    │                                      │ • 检查证书链
    │                                      │ • 验证签名
    │                                      │
    │                   3.4 认证结果       │
    │◄────────────────────────────────────│
    │     认证成功/失败                    │
```

### 11.3 阶段二：Configuration (配置同步)

#### 11.3.1 WPS 自动配置

**目标**: 通过 WPS 协议传输网络配置参数

```c
// 配置同步过程
struct configuration_synchronization {
    // 配置内容
    struct network_configuration {
        // 基本网络参数
        uint8_t  fronthaul_ssid[32];     // 前端 SSID
        uint8_t  fronthaul_passphrase[64]; // 前端密码
        uint8_t  backhaul_ssid[32];      // 回程 SSID
        uint8_t  backhaul_passphrase[64]; // 回程密码

        // 安全配置
        uint16_t auth_type;              // 认证类型
        uint16_t encr_type;              // 加密类型
        uint8_t  wpa3_transition;        // WPA3 过渡模式

        // Multi-AP 配置
        uint8_t  device_role;            // Controller/Agent
        uint8_t  multi_ap_profile;       // 配置文件版本
        uint8_t  supported_bands;        // 支持的频段

        // 网络策略
        struct network_policies {
            bool band_steering;          // 频段引导
            bool load_balancing;         // 负载均衡
            bool fast_roaming;           // 快速漫游
            bool airtime_fairness;       // 空口时间公平性
        } policies;

        // 信道配置
        struct channel_configuration {
            uint8_t  channel_2g;         // 2.4GHz 信道
            uint8_t  channel_5g_low;     // 5GHz 低频段信道
            uint8_t  channel_5g_high;    // 5GHz 高频段信道
            uint8_t  channel_6g;         // 6GHz 信道 (Wi-Fi 6E)
            uint8_t  bandwidth_2g;       // 2.4GHz 带宽
            uint8_t  bandwidth_5g;       // 5GHz 带宽
            uint8_t  bandwidth_6g;       // 6GHz 带宽
        } channels;
    } config;

    // 配置应用状态
    enum config_apply_state {
        CONFIG_RECEIVED,                 // 配置已接收
        CONFIG_VALIDATING,               // 配置验证中
        CONFIG_APPLYING,                 // 配置应用中
        CONFIG_APPLIED,                  // 配置已应用
        CONFIG_FAILED                    // 配置失败
    } apply_state;
};
```

**配置同步执行流程**:

```
步骤 4: WPS 配置传输 (基于前面的 M1-M8 流程)
New Agent                           Controller
    │                                      │
    │ 4.1 启动 WPS 配置会话                │
    │ ────────────────────────────────────►│
    │     发送 AP Autoconfiguration Search │ • 准备配置参数
    │                                      │ • 生成网络凭据
    │                                      │
    │                   4.2 开始 WPS 握手  │
    │◄────────────────────────────────────│
    │     AP Autoconfiguration Response    │
    │                                      │
    │ 4.3 执行 M1-M8 消息交换              │
    │◄───────────────────────────────────►│
    │     (详见前面的 WPS 消息解析)        │
    │                                      │
    │ 4.4 接收网络配置 (M7 消息)           │
    │◄────────────────────────────────────│
    │     包含:                            │
    │     - 前端/回程 SSID 和密钥          │
    │     - 安全参数                       │
    │     - Multi-AP 角色                  │
    │     - 网络策略                       │
    │     - 信道配置                       │
    │                                      │
    │ 4.5 确认配置接收 (M8 消息)           │
    │ ────────────────────────────────────►│
    │                                      │

步骤 5: 配置应用和验证
New Agent                           Controller
    │                                      │
    │ 5.1 验证配置参数                     │
    │     检查配置完整性和有效性           │
    │                                      │
    │ 5.2 应用网络配置                     │
    │     - 配置无线接口                   │
    │     - 设置安全参数                   │
    │     - 启用 Multi-AP 功能             │
    │                                      │
    │ 5.3 重启网络接口                     │
    │     使用新配置重新启动               │
    │                                      │
    │ 5.4 验证配置生效                     │
    │ ────────────────────────────────────►│
    │     发送配置状态报告                 │ • 验证设备配置
    │                                      │ • 检查网络连接
    │                                      │
    │                   5.5 配置确认       │
    │◄────────────────────────────────────│
    │     配置成功确认                     │
```

### 11.4 阶段三：Discovery (拓扑发现)

#### 11.4.1 邻居发现

**目标**: 发现网络中的所有设备并建立拓扑关系

```c
// 拓扑发现过程
struct topology_discovery_process {
    // 发现状态
    enum discovery_state {
        DISCOVERY_INIT,                  // 初始化
        DISCOVERY_NEIGHBOR_SCAN,         // 邻居扫描
        DISCOVERY_TOPOLOGY_BUILD,        // 拓扑构建
        DISCOVERY_PATH_CALCULATION,      // 路径计算
        DISCOVERY_COMPLETE               // 发现完成
    } state;

    // 邻居设备信息
    struct neighbor_device {
        uint8_t  al_mac_address[6];      // 抽象层 MAC
        uint8_t  device_role;            // Controller/Agent
        uint32_t device_capabilities;    // 设备能力

        // 接口信息
        struct device_interface {
            uint8_t  interface_mac[6];   // 接口 MAC
            uint16_t interface_type;     // 接口类型 (Wi-Fi/Ethernet)
            uint8_t  interface_role;     // 前端/回程
            int8_t   rssi;               // 信号强度 (Wi-Fi)
            uint32_t link_speed;         // 链路速度 (Ethernet)
            bool     is_bridge;          // 是否为桥接接口
        } interfaces[MAX_INTERFACES];

        // 链路度量
        struct link_metrics {
            uint32_t tx_packets;         // 发送包数
            uint32_t rx_packets;         // 接收包数
            uint32_t tx_errors;          // 发送错误
            uint32_t rx_errors;          // 接收错误
            uint16_t link_availability;  // 链路可用性 (%)
            uint16_t phy_rate;           // 物理速率 (Mbps)
            uint32_t mac_throughput;     // MAC 吞吐量
        } metrics;
    } neighbors[MAX_NEIGHBORS];

    // 网络拓扑
    struct network_topology {
        uint8_t  controller_al_mac[6];   // Controller AL MAC
        uint32_t total_devices;          // 总设备数
        uint32_t max_hops;               // 最大跳数

        // 拓扑图 (邻接矩阵)
        bool adjacency_matrix[MAX_DEVICES][MAX_DEVICES];

        // 路径表
        struct routing_entry {
            uint8_t  dest_al_mac[6];     // 目标设备
            uint8_t  next_hop_al_mac[6]; // 下一跳设备
            uint8_t  interface_mac[6];   // 出接口
            uint32_t path_cost;          // 路径代价
            uint8_t  hop_count;          // 跳数
        } routing_table[MAX_ROUTES];
    } topology;
};
```

**拓扑发现执行流程**:

```
步骤 6: 邻居发现
New Agent                           Network Devices
    │                                      │
    │ 6.1 发送拓扑发现消息                 │
    │ ────────────────────────────────────►│
    │     IEEE 1905.1 Topology Discovery  │ • 所有设备接收
    │     组播地址: 01:80:C2:00:00:13      │ • 更新邻居表
    │     EtherType: 0x893A                │
    │                                      │
    │◄─── 6.2 拓扑响应消息 ───────────────│
    │     每个邻居设备响应:                │
    │     - 设备 AL MAC                    │
    │     - 接口信息                       │
    │     - 设备能力                       │
    │     - 链路度量                       │
    │                                      │
    │ 6.3 构建邻居表                       │
    │     记录所有直接邻居                 │
    │                                      │

步骤 7: 全网拓扑构建
New Agent                           Controller
    │                                      │
    │ 7.1 发送拓扑查询                     │
    │ ────────────────────────────────────►│
    │     IEEE 1905.1 Topology Query      │ • 查询全网拓扑
    │                                      │ • 收集所有设备信息
    │                                      │
    │                   7.2 拓扑响应       │
    │◄────────────────────────────────────│
    │     包含完整网络拓扑:                │
    │     - 所有设备列表                   │
    │     - 设备间连接关系                 │
    │     - 链路质量信息                   │
    │     - 路径代价                       │
    │                                      │
    │ 7.3 构建本地拓扑数据库               │
    │     - 邻接矩阵                       │
    │     - 设备能力表                     │
    │     - 链路度量表                     │
```

#### 11.4.2 路径计算和路由表建立

```c
// 路径计算算法
struct path_calculation {
    // 路径代价计算
    uint32_t calculate_path_cost(struct link_metrics *metrics) {
        uint32_t cost = 0;

        // 基于链路质量的代价
        cost += (100 - metrics->link_availability) * 10;

        // 基于错误率的代价
        float error_rate = (float)(metrics->tx_errors + metrics->rx_errors) /
                          (metrics->tx_packets + metrics->rx_packets);
        cost += (uint32_t)(error_rate * 1000);

        // 基于带宽的代价
        if (metrics->phy_rate > 0) {
            cost += 100000 / metrics->phy_rate;
        }

        return cost;
    }

    // Dijkstra 最短路径算法
    void calculate_shortest_paths(struct network_topology *topo,
                                 uint8_t source_al_mac[6]) {
        uint32_t dist[MAX_DEVICES];
        uint8_t  prev[MAX_DEVICES][6];
        bool     visited[MAX_DEVICES];

        // 初始化
        for (int i = 0; i < topo->total_devices; i++) {
            dist[i] = UINT32_MAX;
            visited[i] = false;
        }

        int source_idx = find_device_index(topo, source_al_mac);
        dist[source_idx] = 0;

        // Dijkstra 算法主循环
        for (int count = 0; count < topo->total_devices - 1; count++) {
            int u = find_min_distance_vertex(dist, visited, topo->total_devices);
            visited[u] = true;

            for (int v = 0; v < topo->total_devices; v++) {
                if (!visited[v] &&
                    topo->adjacency_matrix[u][v] &&
                    dist[u] != UINT32_MAX) {

                    uint32_t alt = dist[u] + get_link_cost(u, v);
                    if (alt < dist[v]) {
                        dist[v] = alt;
                        memcpy(prev[v], get_device_al_mac(u), 6);
                    }
                }
            }
        }

        // 构建路由表
        build_routing_table(topo, source_al_mac, dist, prev);
    }
};
```

**路径计算执行流程**:

```
步骤 8: 路径计算和路由表建立
New Agent                           Controller
    │                                      │
    │ 8.1 请求路径计算                     │
    │ ────────────────────────────────────►│
    │     发送链路度量查询                 │ • 收集全网链路度量
    │                                      │ • 计算最优路径
    │                                      │ • 运行 Dijkstra 算法
    │                                      │
    │                   8.2 路径计算结果   │
    │◄────────────────────────────────────│
    │     包含:                            │
    │     - 到所有设备的最优路径           │
    │     - 路径代价                       │
    │     - 下一跳信息                     │
    │                                      │
    │ 8.3 建立本地路由表                   │
    │     - 目标设备 → 下一跳映射          │
    │     - 出接口选择                     │
    │     - 备用路径                       │
    │                                      │
    │ 8.4 路由表同步                       │
    │◄───────────────────────────────────►│
    │     与网络中其他设备同步路由信息     │
```

### 11.5 阶段四：Operation (正常运行)

#### 11.5.1 客户端管理

**目标**: 智能管理客户端连接和漫游

```c
// 客户端管理系统
struct client_management_system {
    // 客户端数据库
    struct client_database {
        struct client_entry {
            uint8_t  client_mac[6];      // 客户端 MAC
            uint8_t  current_ap[6];      // 当前关联 AP
            int8_t   rssi;               // 当前 RSSI
            uint32_t association_time;   // 关联时间
            uint32_t last_activity;      // 最后活动时间

            // 客户端能力
            struct client_capabilities {
                bool supports_5ghz;      // 支持 5GHz
                bool supports_6ghz;      // 支持 6GHz
                bool supports_11k;       // 支持 802.11k
                bool supports_11v;       // 支持 802.11v
                bool supports_11r;       // 支持 802.11r
                uint16_t max_phy_rate;   // 最大物理速率
            } capabilities;

            // 流量统计
            struct traffic_stats {
                uint64_t tx_bytes;       // 发送字节数
                uint64_t rx_bytes;       // 接收字节数
                uint32_t tx_packets;     // 发送包数
                uint32_t rx_packets;     // 接收包数
                float    avg_throughput; // 平均吞吐量
            } stats;
        } clients[MAX_CLIENTS];

        uint32_t total_clients;          // 总客户端数
    } client_db;

    // 引导策略
    struct steering_policies {
        // 频段引导
        struct band_steering {
            bool     enabled;            // 是否启用
            int8_t   rssi_threshold_5g;  // 5GHz RSSI 阈值
            uint32_t load_threshold_2g;  // 2.4GHz 负载阈值
            uint32_t retry_interval;     // 重试间隔
        } band_steering;

        // AP 引导
        struct ap_steering {
            bool     enabled;            // 是否启用
            int8_t   rssi_diff_threshold; // RSSI 差异阈值
            uint32_t load_diff_threshold; // 负载差异阈值
            uint32_t steering_timeout;   // 引导超时
        } ap_steering;

        // 负载均衡
        struct load_balancing {
            bool     enabled;            // 是否启用
            uint32_t max_clients_per_ap; // 每 AP 最大客户端
            float    load_threshold;     // 负载阈值
            uint32_t rebalance_interval; // 重平衡间隔
        } load_balancing;
    } policies;
};
```

**客户端管理执行流程**:

```
客户端管理持续过程:
Controller                          Agents
    │                                  │
    │ 9.1 客户端状态监控               │
    │◄─────────────────────────────────│
    │     定期收集:                    │
    │     - 客户端关联状态             │
    │     - RSSI 测量                  │
    │     - 流量统计                   │
    │     - 信道利用率                 │
    │                                  │
    │ 9.2 引导决策                     │
    │     基于策略分析:                │
    │     - 是否需要频段引导           │
    │     - 是否需要 AP 引导           │
    │     - 是否需要负载重分配         │
    │                                  │
    │ 9.3 发送引导指令                 │
    │ ─────────────────────────────────►│
    │     Client Steering Request      │ • 执行引导操作
    │     包含:                        │ • 发送 BTM 请求
    │     - 目标客户端                 │ • 调整信号强度
    │     - 目标 AP/频段               │ • 阻塞关联请求
    │     - 引导方法                   │
    │                                  │
    │◄─── 9.4 引导结果报告 ───────────│
    │     Client Steering BTM Report   │
    │     包含引导成功/失败状态        │
```

#### 11.5.2 性能优化

```c
// 性能优化系统
struct performance_optimization {
    // 信道优化
    struct channel_optimization {
        uint32_t scan_interval;          // 扫描间隔
        uint32_t optimization_interval;  // 优化间隔

        // 信道质量评估
        struct channel_quality {
            uint8_t  channel;            // 信道号
            float    utilization;        // 利用率
            int8_t   noise_floor;        // 噪声底
            uint32_t interference_level; // 干扰水平
            uint32_t overlapping_bss;    // 重叠 BSS 数量
            float    quality_score;      // 质量评分
        } channel_quality[MAX_CHANNELS];

        // 优化算法
        uint8_t select_optimal_channel(struct channel_quality *channels,
                                      int channel_count) {
            uint8_t best_channel = 0;
            float best_score = 0;

            for (int i = 0; i < channel_count; i++) {
                float score = calculate_channel_score(&channels[i]);
                if (score > best_score) {
                    best_score = score;
                    best_channel = channels[i].channel;
                }
            }

            return best_channel;
        }
    } channel_opt;

    // 功率优化
    struct power_optimization {
        int8_t   min_tx_power;           // 最小发射功率
        int8_t   max_tx_power;           // 最大发射功率
        uint32_t adjustment_interval;    // 调整间隔

        // 功率调整算法
        int8_t calculate_optimal_power(struct ap_info *ap) {
            int8_t optimal_power = ap->current_tx_power;

            // 基于客户端 RSSI 调整
            if (ap->min_client_rssi < -70) {
                optimal_power = min(ap->max_tx_power, optimal_power + 3);
            } else if (ap->min_client_rssi > -50) {
                optimal_power = max(ap->min_tx_power, optimal_power - 2);
            }

            // 基于干扰水平调整
            if (ap->interference_level > HIGH_INTERFERENCE) {
                optimal_power = min(ap->max_tx_power, optimal_power + 2);
            }

            return optimal_power;
        }
    } power_opt;
};
```

**性能优化执行流程**:

```
性能优化持续过程:
Controller                          Network
    │                                  │
    │ 10.1 性能监控                    │
    │◄─────────────────────────────────│
    │     收集性能指标:                │
    │     - 吞吐量统计                 │
    │     - 延迟测量                   │
    │     - 信道利用率                 │
    │     - 干扰水平                   │
    │                                  │
    │ 10.2 性能分析                    │
    │     识别性能瓶颈:                │
    │     - 信道拥塞                   │
    │     - 功率不当                   │
    │     - 负载不均                   │
    │                                  │
    │ 10.3 优化决策                    │
    │     制定优化策略:                │
    │     - 信道切换                   │
    │     - 功率调整                   │
    │     - 负载重分配                 │
    │                                  │
    │ 10.4 执行优化                    │
    │ ─────────────────────────────────►│
    │     发送优化指令                 │ • 应用新配置
    │                                  │ • 监控效果
    │                                  │
    │◄─── 10.5 优化结果 ──────────────│
    │     报告优化效果                 │
```

**总结**: EasyMesh 的完整组网流程包括 Onboarding (设备发现和认证)、Configuration (WPS 配置同步)、Discovery (拓扑发现和路径计算)、Operation (客户端管理和性能优化) 四个关键阶段，每个阶段都有详细的执行步骤和技术机制。
```c
// WPS 消息封装在 1905.1 消息中
struct ieee1905_wps_message {
    // 1905.1 消息头
    uint8_t  al_mac_address[6];          // AL MAC 地址
    uint8_t  message_version;            // 消息版本
    uint16_t message_type;               // AP_AUTOCONFIGURATION_WSC_MESSAGE
    uint16_t message_id;                 // 消息 ID
    uint8_t  fragment_id;                // 分片 ID
    uint8_t  indicators;                 // 指示符

    // WSC TLV
    struct wsc_tlv {
        uint8_t  tlv_type;               // TLV 类型 (WSC)
        uint16_t tlv_length;             // TLV 长度
        uint8_t  wsc_frame_type;         // WSC 帧类型
        uint16_t wsc_length;             // WSC 长度
        uint8_t  wsc_data[];             // WSC 数据 (M1-M8)
    } wsc_tlv;
};

// WPS 状态机
enum wps_state {
    WPS_STATE_IDLE,                      // 空闲状态
    WPS_STATE_DISCOVERY,                 // 发现状态
    WPS_STATE_M1_SENT,                   // M1 已发送
    WPS_STATE_M2_RECEIVED,               // M2 已接收
    WPS_STATE_M3_SENT,                   // M3 已发送
    WPS_STATE_M4_RECEIVED,               // M4 已接收
    WPS_STATE_M5_SENT,                   // M5 已发送
    WPS_STATE_M6_RECEIVED,               // M6 已接收
    WPS_STATE_M7_SENT,                   // M7 已发送
    WPS_STATE_M8_RECEIVED,               // M8 已接收
    WPS_STATE_CONFIGURED,                // 配置完成
    WPS_STATE_ERROR                      // 错误状态
};

// WPS 状态机处理
void handle_wps_state_machine(struct wps_session *session,
                             enum wps_state new_state) {
    switch (new_state) {
        case WPS_STATE_M2_RECEIVED:
            // 验证 M2 消息
            if (validate_m2_message(session)) {
                send_m3_message(session);
                session->state = WPS_STATE_M3_SENT;
            } else {
                session->state = WPS_STATE_ERROR;
            }
            break;

        case WPS_STATE_M8_RECEIVED:
            // 提取配置信息
            extract_network_config(session);
            apply_network_config(session);
            send_wsc_done(session);
            session->state = WPS_STATE_CONFIGURED;

            // 启动 1905.1 拓扑发现
            start_topology_discovery();
            break;

        case WPS_STATE_ERROR:
            // 清理会话
            cleanup_wps_session(session);
            break;

        default:
            break;
    }
}
```

#### 3.1.3 动态路径选择
**HWMP 协议借鉴** (Hybrid Wireless Mesh Protocol):
```c
// 路径度量计算
struct path_metric {
    uint32_t link_metric;        // 链路质量度量
    uint32_t hop_count;          // 跳数
    uint32_t bandwidth;          // 可用带宽
    uint32_t delay;              // 传输延迟
    uint32_t reliability;        // 链路可靠性
};

// 最优路径选择算法
int calculate_path_cost(struct path_metric *metric) {
    return (metric->link_metric * LINK_WEIGHT +
            metric->hop_count * HOP_WEIGHT +
            (MAX_BANDWIDTH - metric->bandwidth) * BW_WEIGHT +
            metric->delay * DELAY_WEIGHT);
}
```

### 3.2 信道管理和干扰避免

#### 3.2.1 动态信道选择 (DCS)
```
信道选择算法:
1. 扫描阶段:
   ├── 被动扫描 (监听信标帧)
   ├── 主动扫描 (发送探测请求)
   └── 干扰检测 (非 Wi-Fi 干扰)

2. 评估阶段:
   ├── 信道利用率计算
   ├── RSSI 测量
   ├── 重叠 BSS 检测
   └── 雷达检测 (DFS 信道)

3. 决策阶段:
   ├── 信道质量评分
   ├── 负载均衡考虑
   └── 最优信道选择
```

#### 3.2.2 功率控制机制
```c
// 自适应功率控制
struct power_control {
    int8_t   current_power;      // 当前发射功率 (dBm)
    int8_t   max_power;          // 最大允许功率
    int8_t   min_power;          // 最小有效功率
    uint32_t interference_level; // 干扰水平
    uint32_t client_count;       // 关联客户端数量
};

// 功率调整算法
int8_t adjust_tx_power(struct power_control *pc, int8_t target_rssi) {
    int8_t new_power = pc->current_power;
    
    // 基于干扰水平调整
    if (pc->interference_level > HIGH_INTERFERENCE_THRESHOLD) {
        new_power = min(pc->max_power, new_power + 3);
    } else if (pc->interference_level < LOW_INTERFERENCE_THRESHOLD) {
        new_power = max(pc->min_power, new_power - 2);
    }
    
    // 基于客户端密度调整
    if (pc->client_count > HIGH_DENSITY_THRESHOLD) {
        new_power = max(pc->min_power, new_power - 1);
    }
    
    return new_power;
}
```

## 4. 有线组网机制

### 4.1 以太网回程 (Ethernet Backhaul)

#### 4.1.1 IEEE 1905.1 over Ethernet
```
以太网帧格式:
┌──────────────┬──────────────┬──────────┬─────────────┬─────┐
│ Dest MAC     │ Src MAC      │ EtherType│ 1905.1 Data │ FCS │
│ (6 bytes)    │ (6 bytes)    │ (0x893A) │ (Variable)  │(4B) │
└──────────────┴──────────────┴──────────┴─────────────┴─────┘
```

**关键特性**:
- **优先级**: 有线回程优先于无线回程
- **带宽**: 千兆/万兆以太网提供充足带宽
- **延迟**: 极低延迟，适合实时应用
- **可靠性**: 有线连接稳定性更高

#### 4.1.2 混合回程架构
```
混合回程网络拓扑:
                Internet
                    │
            ┌───────────────┐
            │   Controller  │ (主路由器)
            │   (Gateway)   │
            └───────┬───────┘
                    │ Ethernet
            ┌───────────────┐
            │     Agent     │ (有线连接)
            │   (Switch)    │
            └───────┬───────┘
                    │ Ethernet
            ┌───────────────┐     ┌───────────────┐
            │     Agent     │◄───►│     Agent     │
            │  (Wired AP)   │ WiFi│ (Wireless AP) │
            └───────────────┘     └───────────────┘
```

### 4.2 链路聚合和负载均衡

#### 4.2.1 多路径传输
```c
// 链路聚合配置
struct link_aggregation {
    uint8_t  active_links;       // 活跃链路数量
    uint32_t total_bandwidth;    // 总带宽
    uint8_t  load_balance_mode;  // 负载均衡模式
    struct link_info links[MAX_LINKS];
};

// 负载均衡算法
int select_egress_link(struct link_aggregation *lag, 
                      struct packet_info *pkt) {
    switch (lag->load_balance_mode) {
        case ROUND_ROBIN:
            return (pkt->seq_num % lag->active_links);
        case HASH_BASED:
            return hash_packet(pkt) % lag->active_links;
        case BANDWIDTH_BASED:
            return select_by_bandwidth(lag, pkt->size);
        default:
            return 0;
    }
}
```

## 5. 网络管理和优化

### 5.1 客户端引导 (Client Steering)

#### 5.1.1 频段引导 (Band Steering)
```
频段引导决策流程:
客户端关联请求
        │
        ▼
┌─────────────────┐
│ 检查客户端能力   │ ──► 仅支持 2.4GHz ──► 允许关联 2.4GHz
└─────────────────┘
        │ 支持双频
        ▼
┌─────────────────┐
│ 评估 5GHz 信号   │ ──► RSSI < 阈值 ──► 允许关联 2.4GHz
└─────────────────┘
        │ RSSI 足够
        ▼
┌─────────────────┐
│ 检查 5GHz 负载   │ ──► 负载过高 ──► 允许关联 2.4GHz
└─────────────────┘
        │ 负载正常
        ▼
    拒绝 2.4GHz 关联，引导至 5GHz
```

#### 5.1.2 AP 引导 (AP Steering)
```c
// AP 选择算法
struct ap_selection_criteria {
    int8_t   rssi_threshold;     // RSSI 阈值
    uint32_t load_threshold;     // 负载阈值
    uint32_t channel_util;       // 信道利用率
    uint32_t client_count;       // 客户端数量
    bool     backhaul_quality;   // 回程质量
};

// 最优 AP 选择
int select_best_ap(struct client_info *client, 
                   struct ap_info *ap_list, 
                   int ap_count) {
    int best_ap = -1;
    int best_score = 0;
    
    for (int i = 0; i < ap_count; i++) {
        int score = calculate_ap_score(&ap_list[i], client);
        if (score > best_score) {
            best_score = score;
            best_ap = i;
        }
    }
    
    return best_ap;
}
```

### 5.2 负载均衡机制

#### 5.2.1 动态负载监控
```
负载监控指标:
┌─────────────────────────────────────────┐
│              AP 负载指标                 │
├─────────────────────────────────────────┤
│ • 关联客户端数量                        │
│ • 信道利用率 (%)                        │
│ • 平均吞吐量 (Mbps)                     │
│ • 队列深度                              │
│ • 重传率 (%)                            │
│ • 空口时间利用率                        │
└─────────────────────────────────────────┘
```

#### 5.2.2 负载重分配策略
```c
// 负载重分配触发条件
bool should_trigger_load_balancing(struct ap_info *ap) {
    return (ap->client_count > MAX_CLIENTS_PER_AP ||
            ap->channel_utilization > MAX_CHANNEL_UTIL ||
            ap->average_throughput < MIN_THROUGHPUT ||
            ap->retry_rate > MAX_RETRY_RATE);
}

// 客户端迁移决策
struct migration_decision {
    uint8_t  client_mac[6];      // 目标客户端
    uint8_t  source_ap[6];       // 源 AP
    uint8_t  target_ap[6];       // 目标 AP
    uint32_t migration_reason;   // 迁移原因
    uint32_t expected_benefit;   // 预期收益
};
```

## 6. 协议消息详解

### 6.1 Multi-AP 协议消息类型

#### 6.1.1 AP 自动配置协议 (ALMP)
```c
// ALMP 消息类型定义
enum almp_message_type {
    AP_AUTOCONFIGURATION_SEARCH         = 0x01,
    AP_AUTOCONFIGURATION_RESPONSE       = 0x02,
    AP_AUTOCONFIGURATION_WSC            = 0x03,
    AP_AUTOCONFIGURATION_RENEW          = 0x04,
    PUSH_BUTTON_EVENT_NOTIFICATION      = 0x05,
    PUSH_BUTTON_JOIN_NOTIFICATION       = 0x06
};

// AP 搜索消息结构
struct ap_autoconfiguration_search {
    uint8_t  message_type;               // 0x01
    uint16_t message_id;                 // 消息 ID
    uint8_t  al_mac_address[6];          // AL MAC 地址
    uint8_t  searched_role;              // 搜索的角色 (0x00=Registrar)
    uint8_t  autoconfig_freq_band;       // 自动配置频段
    // TLV 数据
    struct supported_service_tlv services;
    struct searched_service_tlv searched;
};
```

#### 6.1.2 客户端关联控制消息
```c
// 客户端关联控制
enum client_association_control {
    CLIENT_ASSOCIATION_REQUEST          = 0x07,
    CLIENT_STEERING_REQUEST             = 0x08,
    CLIENT_STEERING_BTM_REPORT          = 0x09,
    CLIENT_CAPABILITY_REPORT            = 0x0A,
    BACKHAUL_STEERING_REQUEST           = 0x0B,
    BACKHAUL_STEERING_RESPONSE          = 0x0C
};

// 客户端引导请求
struct client_steering_request {
    uint8_t  message_type;               // 0x08
    uint16_t message_id;
    uint8_t  client_mac[6];              // 客户端 MAC
    uint8_t  request_flags;              // 请求标志
    uint16_t opportunity_window;         // 机会窗口 (秒)
    uint16_t disassociation_timer;       // 解关联定时器
    // 目标 BSS 列表
    struct target_bss_list targets;
};
```

### 6.2 IEEE 1905.1 核心消息

#### 6.2.1 拓扑发现和通知
```c
// 1905.1 消息类型
enum ieee1905_message_type {
    TOPOLOGY_DISCOVERY_MESSAGE          = 0x0000,
    TOPOLOGY_NOTIFICATION_MESSAGE       = 0x0001,
    TOPOLOGY_QUERY_MESSAGE              = 0x0002,
    TOPOLOGY_RESPONSE_MESSAGE           = 0x0003,
    VENDOR_SPECIFIC_MESSAGE             = 0x0004,
    LINK_METRIC_QUERY_MESSAGE           = 0x0005,
    LINK_METRIC_RESPONSE_MESSAGE        = 0x0006,
    AP_AUTOCONFIGURATION_SEARCH_MESSAGE = 0x0007,
    AP_AUTOCONFIGURATION_RESPONSE_MESSAGE = 0x0008,
    AP_AUTOCONFIGURATION_WSC_MESSAGE    = 0x0009,
    AP_AUTOCONFIGURATION_RENEW_MESSAGE  = 0x000A,
    PUSH_BUTTON_EVENT_NOTIFICATION_MESSAGE = 0x000B,
    PUSH_BUTTON_JOIN_NOTIFICATION_MESSAGE = 0x000C
};

// 拓扑发现消息
struct topology_discovery_message {
    uint8_t  al_mac_address[6];          // AL MAC 地址
    uint8_t  message_version;            // 消息版本
    uint16_t message_type;               // 消息类型
    uint16_t message_id;                 // 消息 ID
    uint8_t  fragment_id;                // 分片 ID
    uint8_t  indicators;                 // 指示符
    // TLV 数据
    struct al_mac_address_tlv al_mac;
    struct mac_address_tlv mac_addresses;
};
```

#### 6.2.2 链路度量收集
```c
// 链路度量查询
struct link_metric_query {
    uint8_t  message_type;               // 0x0005
    uint16_t message_id;
    uint8_t  destination;                // 目标类型
    uint8_t  specific_neighbor[6];       // 特定邻居 (可选)
    uint8_t  link_metrics_type;          // 度量类型
};

// 链路度量响应
struct link_metric_response {
    uint8_t  message_type;               // 0x0006
    uint16_t message_id;
    // 度量数据 TLV
    struct transmitter_link_metric_tlv tx_metrics;
    struct receiver_link_metric_tlv rx_metrics;
};

// 发送端链路度量
struct transmitter_link_metric {
    uint8_t  local_interface_mac[6];     // 本地接口 MAC
    uint8_t  neighbor_interface_mac[6];  // 邻居接口 MAC
    uint16_t interface_type;             // 接口类型
    uint8_t  bridge_flag;                // 桥接标志
    uint32_t packet_errors;              // 包错误数
    uint32_t transmitted_packets;        // 发送包数
    uint16_t mac_throughput_capacity;    // MAC 吞吐量容量
    uint16_t link_availability;          // 链路可用性
    uint16_t phy_rate;                   // 物理速率
};
```

## 7. 实际部署场景分析

### 7.1 家庭网络部署

#### 7.1.1 典型家庭拓扑
```
三层别墅 EasyMesh 部署:
                    Internet
                        │
                ┌───────────────┐
                │   Gateway     │ (一层 - 主路由器)
                │  Controller   │ - 2.4G/5G-1: 前端
                └───────┬───────┘ - 5G-2: 回程
                        │ Ethernet
                ┌───────────────┐
                │     Agent     │ (二层 - 有线回程)
                │   (Wired)     │ - 2.4G/5G: 前端
                └───────────────┘ - 通过以太网连接

        ┌───────────────┐     ┌───────────────┐
        │     Agent     │◄───►│     Agent     │ (三层 - 无线回程)
        │  (Wireless)   │ 5G-2│  (Wireless)   │ - 2.4G/5G-1: 前端
        └───────────────┘     └───────────────┘ - 5G-2: 回程
```

#### 7.1.2 信号覆盖优化
```c
// 覆盖优化算法
struct coverage_optimization {
    struct ap_position {
        float x, y, z;                   // 3D 坐标
        float coverage_radius_2g;        // 2.4GHz 覆盖半径
        float coverage_radius_5g;        // 5GHz 覆盖半径
        int8_t tx_power_2g;              // 2.4GHz 发射功率
        int8_t tx_power_5g;              // 5GHz 发射功率
    } ap_positions[MAX_APS];

    struct coverage_hole {
        float x, y, z;                   // 覆盖盲区坐标
        float required_rssi;             // 所需 RSSI
        uint8_t frequency_band;          // 频段
    } coverage_holes[MAX_HOLES];
};

// 功率调整优化
void optimize_coverage(struct coverage_optimization *opt) {
    for (int i = 0; i < opt->ap_count; i++) {
        // 计算当前覆盖区域
        calculate_coverage_area(&opt->ap_positions[i]);

        // 检测覆盖盲区
        detect_coverage_holes(opt);

        // 调整发射功率
        adjust_tx_power_for_coverage(&opt->ap_positions[i], opt->coverage_holes);

        // 验证干扰水平
        check_interference_level(&opt->ap_positions[i]);
    }
}
```

### 7.2 企业网络部署

#### 7.2.1 大规模部署架构
```
企业园区 EasyMesh 部署:
                        Core Network
                             │
                    ┌────────────────┐
                    │   Controller   │ (中心控制器)
                    │   (Cluster)    │ - 集中管理
                    └────────┬───────┘ - 策略下发
                             │ Fiber/Ethernet
            ┌────────────────┼────────────────┐
            │                │                │
    ┌───────────────┐ ┌───────────────┐ ┌───────────────┐
    │     Agent     │ │     Agent     │ │     Agent     │
    │  (Building A) │ │  (Building B) │ │  (Building C) │
    └───────┬───────┘ └───────┬───────┘ └───────┬───────┘
            │                 │                 │
    ┌───────────────┐ ┌───────────────┐ ┌───────────────┐
    │     Agent     │ │     Agent     │ │     Agent     │
    │   (Floor 1)   │ │   (Floor 1)   │ │   (Floor 1)   │
    └───────────────┘ └───────────────┘ └───────────────┘
```

#### 7.2.2 高密度部署优化
```c
// 高密度环境配置
struct high_density_config {
    uint32_t max_clients_per_ap;         // 每 AP 最大客户端数
    uint32_t load_balance_threshold;     // 负载均衡阈值
    uint32_t channel_reuse_distance;     // 信道复用距离
    uint8_t  aggressive_steering;        // 激进引导模式
    uint32_t fast_roaming_enabled;       // 快速漫游
};

// 密集部署信道规划
void plan_channels_high_density(struct ap_deployment *deployment) {
    // 1. 计算 AP 间距离矩阵
    calculate_distance_matrix(deployment);

    // 2. 应用信道复用算法
    for (int i = 0; i < deployment->ap_count; i++) {
        for (int j = i + 1; j < deployment->ap_count; j++) {
            float distance = deployment->distance_matrix[i][j];

            // 同信道复用距离检查
            if (distance < MIN_COCHANNEL_DISTANCE) {
                assign_different_channel(&deployment->aps[i],
                                       &deployment->aps[j]);
            }

            // 邻信道干扰避免
            if (distance < MIN_ADJACENT_DISTANCE) {
                avoid_adjacent_channels(&deployment->aps[i],
                                      &deployment->aps[j]);
            }
        }
    }

    // 3. 功率控制优化
    optimize_power_levels(deployment);
}
```

## 8. 性能监控和故障诊断

### 8.1 关键性能指标 (KPI)

#### 8.1.1 网络层面 KPI
```c
// 网络性能指标
struct network_kpi {
    // 吞吐量指标
    uint64_t total_throughput;           // 总吞吐量 (bps)
    uint64_t per_ap_throughput[MAX_APS]; // 每 AP 吞吐量
    uint64_t backhaul_throughput;        // 回程吞吐量

    // 延迟指标
    uint32_t average_latency;            // 平均延迟 (ms)
    uint32_t max_latency;                // 最大延迟
    uint32_t jitter;                     // 抖动

    // 可靠性指标
    float    packet_loss_rate;           // 丢包率 (%)
    float    retry_rate;                 // 重传率 (%)
    uint32_t connection_drops;           // 连接断开次数

    // 覆盖指标
    float    coverage_percentage;        // 覆盖百分比
    uint32_t dead_zones;                 // 死区数量
    int8_t   min_rssi;                   // 最小 RSSI
};
```

#### 8.1.2 客户端体验 KPI
```c
// 客户端体验指标
struct client_experience_kpi {
    // 连接性能
    uint32_t association_time;           // 关联时间 (ms)
    uint32_t roaming_time;               // 漫游时间 (ms)
    uint32_t authentication_failures;    // 认证失败次数

    // 应用性能
    uint64_t web_page_load_time;         // 网页加载时间 (ms)
    uint64_t video_startup_time;         // 视频启动时间 (ms)
    float    video_quality_score;        // 视频质量评分
    uint32_t voip_mos_score;             // VoIP MOS 评分

    // 移动性能
    uint32_t successful_roams;           // 成功漫游次数
    uint32_t failed_roams;               // 失败漫游次数
    uint32_t ping_pong_roams;            // 乒乓漫游次数
};
```

### 8.2 故障检测和自愈机制

#### 8.2.1 链路故障检测
```c
// 链路健康监控
struct link_health_monitor {
    uint32_t heartbeat_interval;         // 心跳间隔 (ms)
    uint32_t failure_threshold;          // 故障阈值
    uint32_t recovery_threshold;         // 恢复阈值

    struct link_status {
        uint8_t  neighbor_mac[6];         // 邻居 MAC
        uint32_t last_seen;               // 最后可见时间
        uint32_t consecutive_failures;    // 连续失败次数
        float    link_quality;            // 链路质量
        bool     is_active;               // 是否活跃
    } links[MAX_NEIGHBORS];
};

// 故障检测算法
bool detect_link_failure(struct link_health_monitor *monitor,
                        uint8_t neighbor_mac[6]) {
    struct link_status *link = find_link(monitor, neighbor_mac);
    if (!link) return false;

    uint32_t current_time = get_current_time();
    uint32_t time_since_last_seen = current_time - link->last_seen;

    // 超时检测
    if (time_since_last_seen > (monitor->heartbeat_interval * 3)) {
        link->consecutive_failures++;

        // 达到故障阈值
        if (link->consecutive_failures >= monitor->failure_threshold) {
            link->is_active = false;
            trigger_link_recovery(link);
            return true;
        }
    }

    return false;
}
```

#### 8.2.2 自动故障恢复
```c
// 故障恢复策略
enum recovery_strategy {
    STRATEGY_POWER_CYCLE,                // 电源重启
    STRATEGY_CHANNEL_CHANGE,             // 信道切换
    STRATEGY_POWER_ADJUSTMENT,           // 功率调整
    STRATEGY_ROUTE_RECALCULATION,        // 路由重计算
    STRATEGY_BACKUP_ACTIVATION           // 备份激活
};

// 故障恢复处理
void handle_link_failure(struct link_status *failed_link) {
    // 1. 记录故障事件
    log_failure_event(failed_link);

    // 2. 通知网络拓扑变化
    send_topology_notification(failed_link->neighbor_mac);

    // 3. 重新计算路由
    recalculate_routing_table();

    // 4. 激活备份链路
    if (has_backup_link(failed_link)) {
        activate_backup_link(failed_link);
    }

    // 5. 调整负载分配
    redistribute_client_load(failed_link);

    // 6. 尝试恢复故障链路
    schedule_recovery_attempt(failed_link, STRATEGY_POWER_CYCLE);
}
```

## 9. 安全机制

### 9.1 认证和加密

#### 9.1.1 WPA3 集成
```c
// WPA3 安全配置
struct wpa3_config {
    uint8_t  sae_password[64];           // SAE 密码
    uint8_t  pmk[32];                    // 成对主密钥
    uint8_t  pmkid[16];                  // PMK 标识符
    uint32_t sae_commit_retries;         // SAE 提交重试次数
    uint32_t sae_confirm_retries;        // SAE 确认重试次数
    bool     transition_mode;            // 过渡模式 (WPA2/WPA3)
};

// SAE 认证流程
int perform_sae_authentication(struct client_info *client,
                              struct wpa3_config *config) {
    // 1. SAE Commit 阶段
    if (send_sae_commit(client, config) != 0) {
        return -1;
    }

    // 2. SAE Confirm 阶段
    if (send_sae_confirm(client, config) != 0) {
        return -1;
    }

    // 3. 密钥派生
    derive_pairwise_keys(client, config);

    // 4. 4-way 握手
    return perform_4way_handshake(client);
}
```

### 9.2 访问控制

#### 9.2.1 基于角色的访问控制 (RBAC)
```c
// 角色定义
enum user_role {
    ROLE_ADMIN,                          // 管理员
    ROLE_OPERATOR,                       // 操作员
    ROLE_MONITOR,                        // 监控员
    ROLE_GUEST                           // 访客
};

// 权限定义
enum permission {
    PERM_NETWORK_CONFIG,                 // 网络配置
    PERM_AP_MANAGEMENT,                  // AP 管理
    PERM_CLIENT_MANAGEMENT,              // 客户端管理
    PERM_SECURITY_CONFIG,                // 安全配置
    PERM_MONITORING,                     // 监控
    PERM_FIRMWARE_UPDATE                 // 固件更新
};

// 访问控制矩阵
struct access_control_matrix {
    bool permissions[ROLE_COUNT][PERM_COUNT];
} acm = {
    // ADMIN    OPERATOR  MONITOR   GUEST
    {true,     true,     false,    false},  // NETWORK_CONFIG
    {true,     true,     false,    false},  // AP_MANAGEMENT
    {true,     true,     false,    false},  // CLIENT_MANAGEMENT
    {true,     false,    false,    false},  // SECURITY_CONFIG
    {true,     true,     true,     false},  // MONITORING
    {true,     false,    false,    false}   // FIRMWARE_UPDATE
};
```

## 10. 总结

### 10.1 EasyMesh 技术优势

1. **标准化**: 基于 IEEE 1905.1 和 Wi-Fi 联盟规范，确保互操作性
2. **自动化**: 自动配置、自动优化、自动故障恢复
3. **灵活性**: 支持有线和无线回程，适应不同部署场景
4. **可扩展性**: 支持大规模网络部署和管理
5. **智能化**: 基于 AI 的网络优化和客户端引导

### 10.2 关键技术要点

- **协议栈**: IEEE 1905.1 抽象层 + Multi-AP 协议层
- **拓扑发现**: 基于组播的自动拓扑发现和维护
- **回程技术**: 有线优先，无线补充的混合回程架构
- **客户端管理**: 智能引导和负载均衡
- **安全机制**: WPA3 + 证书管理 + 访问控制

### 10.3 部署建议

1. **家庭网络**: 优先使用有线回程，合理规划 AP 位置
2. **企业网络**: 集中控制，分层管理，高密度优化
3. **性能监控**: 建立完善的 KPI 监控体系
4. **故障处理**: 实施主动监控和自动恢复机制
5. **安全防护**: 部署多层安全防护措施

### 10.4 技术发展趋势

1. **AI 驱动优化**: 机器学习算法优化网络性能
2. **Wi-Fi 7 集成**: 支持最新 802.11be 标准
3. **边缘计算**: 在网格节点部署边缘计算能力
4. **IoT 优化**: 针对物联网设备的专门优化
5. **云端管理**: 云端统一管理和监控平台
